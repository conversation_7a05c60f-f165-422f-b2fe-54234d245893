<?php

namespace StudentAttendance\Repositories;

use Admission\Models\StudentDetailsView;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use StudentAttendance\Models\StudentAttendance;
use StudentAttendance\Interfaces\AttendanceInterface;

class AttendanceRepository implements AttendanceInterface
{
    protected $studentAttendance;
    protected $studentDetailsView;

    function __construct(StudentAttendance $studentAttendance, StudentDetailsView $studentDetailsView)
    {
        $this->studentAttendance = $studentAttendance;
        $this->studentDetailsView = $studentDetailsView;
    }

    public function getStudents($data)
    {
        $students = $this->studentDetailsView
            ->leftjoin('student_attendance', function ($join) use ($data) {
                $join->on('student_attendance.student_id', '=', 'student_details_view.id')
                    ->where('student_attendance.date', '=', date('Y-m-d', strtotime($data['date'])));
            })
            ->where('department_id', $data['department_id'])
            ->where('classroom_id', $data['classroom_id'])
            ->where('status', 'ACTIVE')->where('year_id', getActiveYearId())
            ->select(
                'student_details_view.id as sid',
                'student_details_view.first_name',
                'student_details_view.last_name',
                'student_details_view.department_name',
                'student_details_view.class_name',
                'student_details_view.gr_no',
                'student_attendance.*',
            )
            ->get();
        return $students;
    }

    public function statusAttendance($data)
    {
        $data['taken_by'] = Auth::id();

        if ($data['status'] == 'Present') {
            $data['present'] = 1;
        } else {
            $data['present'] = 0;
        }

        $data['date'] = date('Y-m-d', strtotime($data['date']));
        $this->studentAttendance::updateOrCreate(
            [
                'student_id' => $data['student_id'],
                'date' => $data['date']
            ],
            $data
        );
    }

    public function storeAttendance($data)
    {
        $attendanceIds = $data['attendanceData'];
        $data['taken_by'] = Auth::id();

        $data['date'] = date('Y-m-d', strtotime($data['date']));
        foreach ($attendanceIds as $attendance) {
            $this->studentAttendance::updateOrCreate(
                [
                    'student_id' => $attendance['student_id'],
                    'date' => $data['date'],
                    'class_uuid' => Auth::id()
                ],
                [
                    'student_id' => $attendance['student_id'],
                    'date' => $data['date'],
                    'present' => $attendance['present'],
                    'class_uuid' => Auth::id()
                ]
            );
        }
    }

    public function changeDiscipline($data)
    {
        $data['date'] = date('Y-m-d', strtotime($data['date']));

        $attendance = $this->studentAttendance::where('student_id', $data['student_id'])
            ->whereDate("date", $data['date'])->first();

        $attendance->discipline_issue = $data['discipline'] ?? null;
        $attendance->save();
    }

    public function exportAttendance($data)
    {
        $start_date = Carbon::parse($data['start_date']);
        $end_date = Carbon::parse($data['end_date']);

        $dates = [];
        $totalDays = 0;
        for ($date = $start_date; $date->lte($end_date); $date->addDay()) {
            $dates[] = $date->format('Y-m-d');
            $totalDays++;
        }

        $attendanceRecords = $this->studentAttendance
            ->leftJoin('student_details_view', 'student_details_view.id', '=', 'student_attendance.student_id')
            ->whereDate("date", '>=', $data['start_date'])
            ->whereDate("date", '<=', $data['end_date'])
            ->where("student_details_view.classroom_id", '=', $data['classroom_id'])
            ->select(
                'student_details_view.first_name',
                'student_details_view.last_name',
                'student_details_view.gr_no',
                'student_details_view.classroom_id as student_class_id',
                'student_details_view.department_id as student_department_id',
                'student_attendance.student_id',
                'student_attendance.date',
                'student_attendance.present'
            )
            ->orderBy('student_attendance.date')
            ->where('student_attendance.class_uuid', Auth::id())
            ->get();

        $attendance = [];
        foreach ($attendanceRecords as $record) {
            $studentId = $record->student_id;
            $date = $record->date;
            $present = $record->present;

            if (!isset($attendance[$studentId])) {
                $attendance[$studentId] = [
                    'name' => $record->first_name . ' ' . $record->last_name,
                    'gr_no' => $record->gr_no,
                    'attendance' => array_fill_keys($dates, 'N/A'),
                    'presentDays' => 0,
                    'totalDaysWithData' => 0
                ];
            }

            if ($attendance[$studentId]['attendance'][$date] === 'N/A') {
                $attendance[$studentId]['totalDaysWithData']++;
            }

            if ($present == 1) {
                $attendance[$studentId]['attendance'][$date] = 'P';
            } elseif ($present == 2) {
                $attendance[$studentId]['attendance'][$date] = 'H';
            } else {
                $attendance[$studentId]['attendance'][$date] = 'A';
            }
            if ($present == 1 || $present == 2) {
                $attendance[$studentId]['presentDays']++;
            }
        }

        foreach ($attendance as $studentId => &$studentData) {
            $totalDaysWithData = $studentData['totalDaysWithData'] > 0 ? $studentData['totalDaysWithData'] : 1;
            $percentage = ($totalDaysWithData > 0) ? ($studentData['presentDays'] / $totalDaysWithData) * 100 : 0;
            $studentData['percentage'] = number_format($percentage, 2);
        }

        $classroom = getClassroomFromId($data['classroom_id']);
        return [
            'attendance' => $attendance,
            'dates' => $dates,
            'totalDays' => $totalDays,
            'classroom' => $classroom
        ];
    }


    public function getAttendanceForAPI($data, $id)
    {
        $attendance = $this->studentAttendance::where('student_id', $id)
            ->whereDate("date", '>=',  $data['startDate'])
            ->whereDate("date", '<=',  $data['endDate'])->get();

        $formattedAttendance = [];
        foreach ($attendance as $record) {
            $status = $record->present ? 'present' : 'absent';

            $formattedAttendance[$record->date] = [
                'status' => $status,
            ];
        }

        return $formattedAttendance;
    }

    public function getDisciplineIssue($data, $id) {
        $attendance = $this->studentAttendance::where('student_id', $id)
        ->whereDate("date", '>=',  $data['startDate'])
        ->whereDate("date", '<=',  $data['endDate'])
        ->where('discipline_issue', '!=', null)
        ->select('date', 'discipline_issue', 'id')->get();

        return $attendance;
    }
}
