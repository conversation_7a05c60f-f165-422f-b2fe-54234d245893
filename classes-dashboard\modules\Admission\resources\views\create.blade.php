@extends('layouts.app')
@section('content')
<style>
    .firstcap 
    {
        text-transform:capitalize;
    }
</style>
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-9">
                <h1>Admission</h1>
            </div>
        </div>
    </div>
</div>
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card card-default">
                    <div class="card-body p-0">
                        <div class="bs-stepper">
                            <div class="bs-stepper-header" role="tablist">
                                <!-- your steps here -->
                                <div class="step" data-target="#student-details">
                                    <button type="button" class="step-trigger" role="tab" aria-controls="student-details" id="student-details-trigger">
                                        <span class="bs-stepper-circle">1</span>
                                        <span class="bs-stepper-label">Student Details</span>
                                    </button>
                                </div>
                                <div class="line"></div>
                                <div class="step" data-target="#parents-details">
                                    <button type="button" class="step-trigger" role="tab" aria-controls="parents-details" id="parents-details-trigger">
                                        <span class="bs-stepper-circle">2</span>
                                        <span class="bs-stepper-label">Parents Details</span>
                                    </button>
                                </div>
                                <div class="line"></div>
                                <div class="step" data-target="#siblings-details">
                                    <button type="button" class="step-trigger" role="tab" aria-controls="siblings-details" id="siblings-details-trigger">
                                        <span class="bs-stepper-circle">3</span>
                                        <span class="bs-stepper-label">Siblings Details</span>
                                    </button>
                                </div>
                                <div class="line"></div>
                                <div class="step" data-target="#health-details">
                                    <button type="button" class="step-trigger" role="tab" aria-controls="health-details" id="health-details-trigger">
                                        <span class="bs-stepper-circle">4</span>
                                        <span class="bs-stepper-label">Health Details</span>
                                    </button>
                                </div>
                                <div class="line"></div>
                                <div class="step" data-target="#past-details">
                                    <button type="button" class="step-trigger" role="tab" aria-controls="past-details" id="past-details-trigger">
                                        <span class="bs-stepper-circle">5</span>
                                        <span class="bs-stepper-label">Past Details</span>
                                    </button>
                                </div>
                            </div>
                            <div class="bs-stepper-content">
                            @include('Admission::steps.student-details')
                            @include('Admission::steps.parents-details')
                            @include('Admission::steps.siblings-details')
                            @include('Admission::steps.health-details')
                            @include('Admission::steps.past-details')
                            </div>
                        </div>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
        </div>
    </div>
</section>
@endsection
@section('scripts')
<script src="
https://cdn.jsdelivr.net/npm/bs-stepper@1.7.0/dist/js/bs-stepper.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        window.stepper = new Stepper(document.querySelector('.bs-stepper'))
    })
</script>
<script>
     var admissionCreateRoute = {
        storeStudentDetails: "{{ route('storeStudentDetails') }}",
        storeStudentParentsDetails: "{{ route('storeStudentParentsDetails') }}",
        storeStudentSiblingsDetails: "{{ route('storeStudentSiblingsDetails') }}",
        storeStudentHealthDetails: "{{ route('storeStudentHealthDetails') }}",
        storeStudentPastDetails: "{{ route('storeStudentPastDetails') }}",
    };
</script>
<script src="{{ asset(mix('js/page-level-js/Admission/js/create.js')) }}"></script>
{!! JsValidator::formRequest('Admission\Http\Requests\CreateStudentDetailsRequest', '#student-details-forms') !!}
{!! JsValidator::formRequest('Admission\Http\Requests\CreateStudentParentsDetailsRequest', '#parents-details-forms') !!}
{!! JsValidator::formRequest('Admission\Http\Requests\CreateStudentSiblingsDetailsRequest', '#siblings-details-forms') !!}
{!! JsValidator::formRequest('Admission\Http\Requests\CreateStudentHealthDetailsRequest', '#health-details-forms') !!}
{!! JsValidator::formRequest('Admission\Http\Requests\CreateStudentPastDetailsRequest', '#past-details-forms') !!}

@if(isset($data))
<script>
    $('.firststep').removeClass('d-none');
    let href = $('.gotourl').attr('href').replace(':id', {{$data->id}});
    $('.gotourl').attr('href', href);

    var data = {!! json_encode($data); !!};
    $.each(data, function(fieldName, value) {
        if(fieldName != "photo") {
            $('[name=' + fieldName + ']').val(value);
        } else {
            $('.image-product-logo').attr('src', window.location.origin + checkHost() + "/storage/student_img/"+value);
        }
    });

    var academicdata = {!! json_encode($data->getAcademicInfo); !!};
    $.each(academicdata, function(fieldName, value) {
        $('[name=' + fieldName + ']').val(value);
    });

    var parentdata = {!! json_encode($data->getParentInfo); !!};
    $.each(parentdata, function(fieldName, value) {
        $('[name=' + fieldName + ']').val(value);
    });

    var healthdata = {!! json_encode($data->getHealthInfo); !!};
    $.each(healthdata, function(fieldName, value) {
        $('[name=' + fieldName + ']').val(value);
    });

    var pastdata = {!! json_encode($data->getPastInfo); !!};
    $.each(pastdata, function(fieldName, value) {
        $('[name=' + fieldName + ']').val(value);
    });

    var siblingdata = {!! json_encode($data->getSiblingsInfo); !!};
    $.each(siblingdata, function(fieldName, value) {
        $('[name=' + fieldName + ']').val(value);
    });

    $('[name="student_id"]').val("{{$data->id}}");
    $('#department').trigger('change');
    $('#waypoint').trigger('change');

    setTimeout(function (){
        console.log("{{ $data->getAcademicInfo->route }}")
            $('#route').val("{{ $data->getAcademicInfo->route }}").trigger('change');          
    }, 2000);

    setTimeout(function (){
            $('#classroom').val("{{ $data->getAcademicInfo->classroom }}").trigger('change');    
    }, 2000);
</script>
@endif
@endsection