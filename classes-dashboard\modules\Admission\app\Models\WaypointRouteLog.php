<?php

namespace Admission\Models;

use Routes\Models\Route;
use Waypoint\Models\Waypoint;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class WaypointRouteLog extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'waypoint_route_logs';

    protected $fillable = [
        'student_id',
        'old_waypoint_id',
        'old_route_id',
        'new_waypoint_id',
        'new_route_id',
        'created_by',
    ];

    public function oldWaypoint()
    {
        return $this->belongsTo(Waypoint::class, 'old_waypoint_id');
    }

    public function oldRoute()
    {
        return $this->belongsTo(Route::class, 'old_route_id');
    }

    public function newWaypoint()
    {
        return $this->belongsTo(Waypoint::class, 'new_waypoint_id');
    }

    public function newRoute()
    {
        return $this->belongsTo(Route::class, 'new_route_id');
    }
}