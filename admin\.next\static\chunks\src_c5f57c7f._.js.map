{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/components/ui/table.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Table({ className, ...props }: React.ComponentProps<'table'>) {\r\n  return (\r\n    <div data-slot=\"table-container\" className=\"relative w-full overflow-x-auto\">\r\n      <table\r\n        data-slot=\"table\"\r\n        className={cn('w-full caption-bottom text-sm', className)}\r\n        {...props}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction TableHeader({ className, ...props }: React.ComponentProps<'thead'>) {\r\n  return <thead data-slot=\"table-header\" className={cn('[&_tr]:border-b', className)} {...props} />;\r\n}\r\n\r\nfunction TableBody({ className, ...props }: React.ComponentProps<'tbody'>) {\r\n  return (\r\n    <tbody\r\n      data-slot=\"table-body\"\r\n      className={cn('[&_tr:last-child]:border-0', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableFooter({ className, ...props }: React.ComponentProps<'tfoot'>) {\r\n  return (\r\n    <tfoot\r\n      data-slot=\"table-footer\"\r\n      className={cn('bg-muted/50 border-t font-medium [&>tr]:last:border-b-0', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableRow({ className, ...props }: React.ComponentProps<'tr'>) {\r\n  return (\r\n    <tr\r\n      data-slot=\"table-row\"\r\n      className={cn(\r\n        'hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableHead({ className, ...props }: React.ComponentProps<'th'>) {\r\n  return (\r\n    <th\r\n      data-slot=\"table-head\"\r\n      className={cn(\r\n        'text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableCell({ className, ...props }: React.ComponentProps<'td'>) {\r\n  return (\r\n    <td\r\n      data-slot=\"table-cell\"\r\n      className={cn(\r\n        'p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableCaption({ className, ...props }: React.ComponentProps<'caption'>) {\r\n  return (\r\n    <caption\r\n      data-slot=\"table-caption\"\r\n      className={cn('text-muted-foreground mt-4 text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Table, TableHeader, TableBody, TableFooter, TableHead, TableRow, TableCell, TableCaption };\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,6LAAC;QAAI,aAAU;QAAkB,WAAU;kBACzC,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAVS;AAYT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBAAO,6LAAC;QAAM,aAAU;QAAe,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAa,GAAG,KAAK;;;;;;AAC/F;MAFS;AAIT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2DAA2D;QACxE,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAwC;IAC5E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/app-components/dataTable.tsx"], "sourcesContent": ["import {\r\n  useReactTable,\r\n  getCoreRowModel,\r\n  ColumnDef,\r\n  flexRender,\r\n} from \"@tanstack/react-table\";\r\n\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"@/components/ui/table\";\r\n\r\ninterface DataTableProps<T> {\r\n  columns: ColumnDef<T>[];\r\n  data: T[];\r\n  isLoading?: boolean;\r\n  getRowClassName?: (row: T, index: number) => string;\r\n}\r\n\r\nexport function DataTable<T>({\r\n  columns,\r\n  data,\r\n  isLoading,\r\n  getRowClassName,\r\n}: DataTableProps<T>) {\r\n  const table = useReactTable({\r\n    data,\r\n    columns,\r\n    getCoreRowModel: getCoreRowModel(),\r\n  });\r\n\r\n  return (\r\n    <div>\r\n      {isLoading ? (\r\n        <div className=\"flex justify-center items-center h-64\">\r\n          <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary\"></div>\r\n        </div>\r\n      ) : (\r\n        <div className=\"rounded-md border\">\r\n          <Table>\r\n            <TableHeader className=\"sticky top-0 bg-muted z-10\">\r\n              {table.getHeaderGroups().map((headerGroup) => (\r\n                <TableRow key={headerGroup.id}>\r\n                  {headerGroup.headers.map((header) => (\r\n                    <TableHead key={header.id}>\r\n                      {header.isPlaceholder\r\n                        ? null\r\n                        : flexRender(\r\n                            header.column.columnDef.header,\r\n                            header.getContext()\r\n                          )}\r\n                    </TableHead>\r\n                  ))}\r\n                </TableRow>\r\n              ))}\r\n            </TableHeader>\r\n            <TableBody>\r\n              {table.getRowModel().rows?.length ? (\r\n                table.getRowModel().rows.map((row, index) => {\r\n                  const customClassName = getRowClassName\r\n                    ? getRowClassName(row.original, index)\r\n                    : \"\";\r\n                  return (\r\n                    <TableRow\r\n                      key={row.id}\r\n                      className={`hover:bg-gray-50 ${customClassName}`}\r\n                    >\r\n                      {row.getVisibleCells().map((cell) => (\r\n                        <TableCell key={cell.id}>\r\n                          {flexRender(\r\n                            cell.column.columnDef.cell,\r\n                            cell.getContext()\r\n                          )}\r\n                        </TableCell>\r\n                      ))}\r\n                    </TableRow>\r\n                  );\r\n                })\r\n              ) : (\r\n                <TableRow>\r\n                  <TableCell\r\n                    colSpan={columns.length}\r\n                    className=\"text-center py-4\"\r\n                  >\r\n                    No data found.\r\n                  </TableCell>\r\n                </TableRow>\r\n              )}\r\n            </TableBody>\r\n          </Table>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAOA;;;;;AAgBO,SAAS,UAAa,EAC3B,OAAO,EACP,IAAI,EACJ,SAAS,EACT,eAAe,EACG;;IAClB,MAAM,QAAQ,CAAA,GAAA,yLAAA,CAAA,gBAAa,AAAD,EAAE;QAC1B;QACA;QACA,iBAAiB,CAAA,GAAA,wKAAA,CAAA,kBAAe,AAAD;IACjC;IAEA,qBACE,6LAAC;kBACE,0BACC,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;iCAGjB,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;;kCACJ,6LAAC,oIAAA,CAAA,cAAW;wBAAC,WAAU;kCACpB,MAAM,eAAe,GAAG,GAAG,CAAC,CAAC,4BAC5B,6LAAC,oIAAA,CAAA,WAAQ;0CACN,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,uBACxB,6LAAC,oIAAA,CAAA,YAAS;kDACP,OAAO,aAAa,GACjB,OACA,CAAA,GAAA,yLAAA,CAAA,aAAU,AAAD,EACP,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;uCALT,OAAO,EAAE;;;;;+BAFd,YAAY,EAAE;;;;;;;;;;kCAcjC,6LAAC,oIAAA,CAAA,YAAS;kCACP,MAAM,WAAW,GAAG,IAAI,EAAE,SACzB,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK;4BACjC,MAAM,kBAAkB,kBACpB,gBAAgB,IAAI,QAAQ,EAAE,SAC9B;4BACJ,qBACE,6LAAC,oIAAA,CAAA,WAAQ;gCAEP,WAAW,CAAC,iBAAiB,EAAE,iBAAiB;0CAE/C,IAAI,eAAe,GAAG,GAAG,CAAC,CAAC,qBAC1B,6LAAC,oIAAA,CAAA,YAAS;kDACP,CAAA,GAAA,yLAAA,CAAA,aAAU,AAAD,EACR,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAC1B,KAAK,UAAU;uCAHH,KAAK,EAAE;;;;;+BAJpB,IAAI,EAAE;;;;;wBAajB,mBAEA,6LAAC,oIAAA,CAAA,WAAQ;sCACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;gCACR,SAAS,QAAQ,MAAM;gCACvB,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;GA3EgB;;QAMA,yLAAA,CAAA,gBAAa;;;KANb", "debugId": null}}, {"offset": {"line": 272, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/components/ui/select.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as SelectPrimitive from '@radix-ui/react-select';\r\nimport { CheckIcon, ChevronDownIcon, ChevronUpIcon } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Select({ ...props }: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />;\r\n}\r\n\r\nfunction SelectGroup({ ...props }: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />;\r\n}\r\n\r\nfunction SelectValue({ ...props }: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />;\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  size = 'default',\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\r\n  size?: 'sm' | 'default';\r\n}) {\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      data-slot=\"select-trigger\"\r\n      data-size={size}\r\n      className={cn(\r\n        \"border-input mt-2 data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-full items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <div className=\"flex-1 text-left\">\r\n        {children}\r\n      </div>\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className=\"size-4 opacity-50 ml-2\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  );\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = 'popper',\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot=\"select-content\"\r\n        className={cn(\r\n          'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md',\r\n          position === 'popper' &&\r\n            'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1',\r\n          className\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            'p-1',\r\n            position === 'popper' &&\r\n              'h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1'\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  );\r\n}\r\n\r\nfunction SelectLabel({ className, ...props }: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot=\"select-label\"\r\n      className={cn('text-muted-foreground px-2 py-2.5 text-xs', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot=\"select-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-2.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  );\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot=\"select-separator\"\r\n      className={cn('bg-border pointer-events-none -mx-1 my-1 h-px', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot=\"select-scroll-up-button\"\r\n      className={cn('flex cursor-default items-center justify-center py-1', className)}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  );\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot=\"select-scroll-down-button\"\r\n      className={cn('flex cursor-default items-center justify-center py-1', className)}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  );\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EAAE,GAAG,OAA0D;IAC7E,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAFS;AAIT,SAAS,YAAY,EAAE,GAAG,OAA2D;IACnF,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAFS;AAIT,SAAS,YAAY,EAAE,GAAG,OAA2D;IACnF,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAFS;AAIT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oyBACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAI,WAAU;0BACZ;;;;;;0BAEH,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MA1BS;AA4BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAA2D;IAC9F,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;QACrE,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAbS;AAeT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;QACrE,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAbS", "debugId": null}}, {"offset": {"line": 528, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/services/testimonialApi.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\nimport { Testimonial } from '@/lib/types';\r\n\r\ninterface TestimonialResponse {\r\n  testimonials: Testimonial[];\r\n  total: number;\r\n  pages: number;\r\n  currentPage: number;\r\n}\r\n\r\nexport const getTestimonials = async (\r\n  page: number = 1,\r\n  limit: number = 10,\r\n  status?: 'PENDING' | 'APPROVED' | 'REJECTED'\r\n): Promise<TestimonialResponse> => {\r\n  try {\r\n    const response = await axiosInstance.get('/testimonials', {\r\n      params: { page, limit, status }\r\n    });\r\n    return response.data;\r\n  } catch (error: any) {\r\n    throw new Error(error.response?.data?.message || `Failed to fetch testimonials: ${error.message}`);\r\n  }\r\n};\r\n\r\nexport const updateTestimonialStatus = async (\r\n  id: string,\r\n  status: 'PENDING' | 'APPROVED' | 'REJECTED'\r\n): Promise<Testimonial> => {\r\n  try {\r\n    const response = await axiosInstance.patch(`/testimonials/${id}/status`, { status });\r\n\r\n    return response.data.data || response.data;\r\n  } catch (error: any) {\r\n    console.error('Error updating testimonial status:', error);\r\n    throw new Error(error.response?.data?.message || `Failed to update testimonial status: ${error.message}`);\r\n  }\r\n};\r\n\r\nexport const deleteTestimonial = async (id: string): Promise<void> => {\r\n  try {\r\n    await axiosInstance.delete(`/testimonials/admin/${id}`);\r\n  } catch (error: any) {\r\n    throw new Error(error.response?.data?.message || `Failed to delete testimonial: ${error.message}`);\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;AAAA;;AAUO,MAAM,kBAAkB,OAC7B,OAAe,CAAC,EAChB,QAAgB,EAAE,EAClB;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,iBAAiB;YACxD,QAAQ;gBAAE;gBAAM;gBAAO;YAAO;QAChC;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,MAAM,IAAI,MAAM,MAAM,QAAQ,EAAE,MAAM,WAAW,CAAC,8BAA8B,EAAE,MAAM,OAAO,EAAE;IACnG;AACF;AAEO,MAAM,0BAA0B,OACrC,IACA;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,KAAK,CAAC,CAAC,cAAc,EAAE,GAAG,OAAO,CAAC,EAAE;YAAE;QAAO;QAElF,OAAO,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI;IAC5C,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,sCAAsC;QACpD,MAAM,IAAI,MAAM,MAAM,QAAQ,EAAE,MAAM,WAAW,CAAC,qCAAqC,EAAE,MAAM,OAAO,EAAE;IAC1G;AACF;AAEO,MAAM,oBAAoB,OAAO;IACtC,IAAI;QACF,MAAM,sHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,CAAC,oBAAoB,EAAE,IAAI;IACxD,EAAE,OAAO,OAAY;QACnB,MAAM,IAAI,MAAM,MAAM,QAAQ,EAAE,MAAM,WAAW,CAAC,8BAA8B,EAAE,MAAM,OAAO,EAAE;IACnG;AACF", "debugId": null}}, {"offset": {"line": 576, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/app-components/pagination.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport {\r\n  ChevronsLeftIcon,\r\n  ChevronsRightIcon,\r\n  ChevronLeftIcon,\r\n  ChevronRightIcon,\r\n} from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\ninterface PaginationProps {\r\n  page: number;\r\n  totalPages: number;\r\n  setPage: (page: number) => void;\r\n  entriesText?: string;\r\n}\r\n\r\nconst pagination = ({\r\n  page,\r\n  totalPages,\r\n  setPage,\r\n  entriesText,\r\n}: PaginationProps) => {\r\n  return (\r\n    <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 px-4 py-2\">\r\n      {entriesText && (\r\n        <div className=\"text-sm text-muted-foreground\">{entriesText}</div>\r\n      )}\r\n\r\n      <div className=\"flex items-center gap-2\">\r\n        <Button\r\n          variant=\"outline\"\r\n          size=\"icon\"\r\n          onClick={() => setPage(1)}\r\n          disabled={page === 1 || page > totalPages}\r\n        >\r\n          <ChevronsLeftIcon className=\"h-4 w-4\" />\r\n        </Button>\r\n        <Button\r\n          variant=\"outline\"\r\n          size=\"icon\"\r\n          onClick={() => setPage(page - 1)}\r\n          disabled={page === 1 || page > totalPages}\r\n        >\r\n          <ChevronLeftIcon className=\"h-4 w-4\" />\r\n        </Button>\r\n        <span className=\"text-sm\">\r\n          Page {page} of {totalPages}\r\n        </span>\r\n        <Button\r\n          variant=\"outline\"\r\n          size=\"icon\"\r\n          onClick={() => setPage(page + 1)}\r\n          disabled={page === totalPages || page > totalPages}\r\n        >\r\n          <ChevronRightIcon className=\"h-4 w-4\" />\r\n        </Button>\r\n        <Button\r\n          variant=\"outline\"\r\n          size=\"icon\"\r\n          onClick={() => setPage(totalPages)}\r\n          disabled={page === totalPages || page > totalPages}\r\n        >\r\n          <ChevronsRightIcon className=\"h-4 w-4\" />\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default pagination;\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAMA;AARA;;;;AAiBA,MAAM,aAAa,CAAC,EAClB,IAAI,EACJ,UAAU,EACV,OAAO,EACP,WAAW,EACK;IAChB,qBACE,6LAAC;QAAI,WAAU;;YACZ,6BACC,6LAAC;gBAAI,WAAU;0BAAiC;;;;;;0BAGlD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,QAAQ;wBACvB,UAAU,SAAS,KAAK,OAAO;kCAE/B,cAAA,6LAAC,6NAAA,CAAA,mBAAgB;4BAAC,WAAU;;;;;;;;;;;kCAE9B,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,QAAQ,OAAO;wBAC9B,UAAU,SAAS,KAAK,OAAO;kCAE/B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;4BAAC,WAAU;;;;;;;;;;;kCAE7B,6LAAC;wBAAK,WAAU;;4BAAU;4BAClB;4BAAK;4BAAK;;;;;;;kCAElB,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,QAAQ,OAAO;wBAC9B,UAAU,SAAS,cAAc,OAAO;kCAExC,cAAA,6LAAC,6NAAA,CAAA,mBAAgB;4BAAC,WAAU;;;;;;;;;;;kCAE9B,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,QAAQ;wBACvB,UAAU,SAAS,cAAc,OAAO;kCAExC,cAAA,6LAAC,+NAAA,CAAA,oBAAiB;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKvC;uCAEe", "debugId": null}}, {"offset": {"line": 708, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/app-components/ConfirmDialog.tsx"], "sourcesContent": ["import {\r\n  AlertDialog,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n  AlertDialogAction,\r\n} from \"@/components/ui/alert-dialog\";\r\n\r\ninterface ConfirmDialogProps {\r\n  open: boolean;\r\n  setOpen: (open: boolean) => void;\r\n  title?: string;\r\n  description?: string;\r\n  confirmText?: string;\r\n  cancelText?: string;\r\n  onConfirm: () => void;\r\n  isLoading?: boolean;\r\n  confirmClassName?: string;\r\n}\r\n\r\nconst ConfirmDialog = ({\r\n  open,\r\n  setOpen,\r\n  title = \"Are you sure?\",\r\n  description = \"This action cannot be undone.\",\r\n  confirmText = \"Delete\",\r\n  cancelText = \"Cancel\",\r\n  onConfirm,\r\n  isLoading = false,\r\n  confirmClassName = \"bg-red-500 hover:bg-red-600\",\r\n}: ConfirmDialogProps) => {\r\n  return (\r\n    <AlertDialog open={open} onOpenChange={setOpen}>\r\n      <AlertDialogContent>\r\n        <AlertDialogHeader>\r\n          <AlertDialogTitle>{title}</AlertDialogTitle>\r\n          <AlertDialogDescription>{description}</AlertDialogDescription>\r\n        </AlertDialogHeader>\r\n        <AlertDialogFooter>\r\n          <AlertDialogCancel disabled={isLoading}>\r\n            {cancelText}\r\n          </AlertDialogCancel>\r\n          <AlertDialogAction\r\n            onClick={onConfirm}\r\n            className={confirmClassName}\r\n            disabled={isLoading}\r\n          >\r\n            {isLoading ? \"Please wait...\" : confirmText}\r\n          </AlertDialogAction>\r\n        </AlertDialogFooter>\r\n      </AlertDialogContent>\r\n    </AlertDialog>\r\n  );\r\n};\r\n\r\nexport default ConfirmDialog;\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAuBA,MAAM,gBAAgB,CAAC,EACrB,IAAI,EACJ,OAAO,EACP,QAAQ,eAAe,EACvB,cAAc,+BAA+B,EAC7C,cAAc,QAAQ,EACtB,aAAa,QAAQ,EACrB,SAAS,EACT,YAAY,KAAK,EACjB,mBAAmB,6BAA6B,EAC7B;IACnB,qBACE,6LAAC,8IAAA,CAAA,cAAW;QAAC,MAAM;QAAM,cAAc;kBACrC,cAAA,6LAAC,8IAAA,CAAA,qBAAkB;;8BACjB,6LAAC,8IAAA,CAAA,oBAAiB;;sCAChB,6LAAC,8IAAA,CAAA,mBAAgB;sCAAE;;;;;;sCACnB,6LAAC,8IAAA,CAAA,yBAAsB;sCAAE;;;;;;;;;;;;8BAE3B,6LAAC,8IAAA,CAAA,oBAAiB;;sCAChB,6LAAC,8IAAA,CAAA,oBAAiB;4BAAC,UAAU;sCAC1B;;;;;;sCAEH,6LAAC,8IAAA,CAAA,oBAAiB;4BAChB,SAAS;4BACT,WAAW;4BACX,UAAU;sCAET,YAAY,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;AAM5C;KAjCM;uCAmCS", "debugId": null}}, {"offset": {"line": 794, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/components/TestimonialTable.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useCallback } from \"react\";\r\nimport { ColumnDef } from \"@tanstack/react-table\";\r\nimport { DataTable } from \"@/app-components/dataTable\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\nimport { Loader2, Trash2, Star } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { toast } from \"sonner\";\r\nimport Image from \"next/image\";\r\nimport { Testimonial } from \"@/lib/types\";\r\nimport {\r\n  getTestimonials,\r\n  updateTestimonialStatus,\r\n  deleteTestimonial,\r\n} from \"@/services/testimonialApi\";\r\nimport Pagination from \"@/app-components/pagination\";\r\nimport ConfirmDialog from \"@/app-components/ConfirmDialog\";\r\n\r\nconst PAGE_SIZE = 10;\r\n\r\nconst TestimonialTable = () => {\r\n  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [totalPages, setTotalPages] = useState(1);\r\n  const [isDeleting, setIsDeleting] = useState(false);\r\n  const [updatingTestimonialId, setUpdatingTestimonialId] = useState<\r\n    string | null\r\n  >(null);\r\n  const [testimonialToDelete, setTestimonialToDelete] = useState<string | null>(\r\n    null\r\n  );\r\n  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);\r\n  const [statusFilter, setStatusFilter] = useState<\r\n    \"ALL\" | \"PENDING\" | \"APPROVED\" | \"REJECTED\"\r\n  >(\"ALL\");\r\n\r\n  const fetchTestimonials = useCallback(\r\n    async (\r\n      page: number,\r\n      status?: \"PENDING\" | \"APPROVED\" | \"REJECTED\" | \"ALL\"\r\n    ) => {\r\n      try {\r\n        setIsLoading(true);\r\n        const filterStatus =\r\n          status && status !== \"ALL\"\r\n            ? (status as \"PENDING\" | \"APPROVED\" | \"REJECTED\")\r\n            : undefined;\r\n        const response = await getTestimonials(page, PAGE_SIZE, filterStatus);\r\n\r\n        setTestimonials(response.testimonials || []);\r\n        setTotalPages(response.pages || 1);\r\n      } catch (error: any) {\r\n        toast.error(error.message || \"Failed to fetch testimonials\");\r\n        setTestimonials([]);\r\n        setTotalPages(1);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    },\r\n    []\r\n  );\r\n\r\n  useEffect(() => {\r\n    fetchTestimonials(1);\r\n  }, [fetchTestimonials]);\r\n\r\n  const handleFilter = () => {\r\n    setCurrentPage(1);\r\n    fetchTestimonials(1, statusFilter);\r\n  };\r\n\r\n  const handlePageChange = (page: number) => {\r\n    if (page >= 1 && page <= totalPages) {\r\n      setCurrentPage(page);\r\n      fetchTestimonials(page, statusFilter);\r\n    }\r\n  };\r\n\r\n  const handleStatusChange = async (\r\n    testimonialId: string,\r\n    status: \"PENDING\" | \"APPROVED\" | \"REJECTED\"\r\n  ) => {\r\n    const previousTestimonials = [...testimonials];\r\n\r\n    try {\r\n      setUpdatingTestimonialId(testimonialId);\r\n\r\n      setTestimonials(\r\n        testimonials.map((t) =>\r\n          t.id === testimonialId\r\n            ? { ...t, status, updatedAt: new Date().toISOString() }\r\n            : t\r\n        )\r\n      );\r\n\r\n      const updatedTestimonial = await updateTestimonialStatus(\r\n        testimonialId,\r\n        status\r\n      );\r\n\r\n      if (updatedTestimonial) {\r\n        setTestimonials(\r\n          testimonials.map((t) =>\r\n            t.id === testimonialId\r\n              ? { ...updatedTestimonial, class: t.class }\r\n              : t\r\n          )\r\n        );\r\n        toast.success(`Testimonial status updated to ${status}`);\r\n      }\r\n    } catch (error: any) {\r\n      console.error(\"Error updating testimonial status:\", error);\r\n      setTestimonials(previousTestimonials);\r\n      toast.error(error.message || \"Failed to update testimonial status\");\r\n    } finally {\r\n      setUpdatingTestimonialId(null);\r\n    }\r\n  };\r\n\r\n  const openDeleteConfirmation = (id: string) => {\r\n    setTestimonialToDelete(id);\r\n    setIsDeleteDialogOpen(true);\r\n  };\r\n\r\n  const handleDeleteTestimonial = async () => {\r\n    if (!testimonialToDelete) return;\r\n\r\n    setIsDeleting(true); // Start loader\r\n\r\n    try {\r\n      await deleteTestimonial(testimonialToDelete);\r\n      toast.success(\"Testimonial deleted successfully!\");\r\n      fetchTestimonials(currentPage);\r\n    } catch (error: any) {\r\n      toast.error(error.message || \"Failed to delete testimonial\");\r\n    } finally {\r\n      setIsDeleting(false); // Stop loader\r\n      setIsDeleteDialogOpen(false);\r\n      setTestimonialToDelete(null);\r\n    }\r\n  };\r\n\r\n  const renderStars = (rating: number) => (\r\n    <div className=\"flex gap-1\">\r\n      {[...Array(5)].map((_, i) => (\r\n        <Star\r\n          key={i}\r\n          className={`w-4 h-4 ${\r\n            i < rating ? \"fill-yellow-400 text-yellow-400\" : \"text-gray-300\"\r\n          }`}\r\n        />\r\n      ))}\r\n    </div>\r\n  );\r\n\r\n  const columns: ColumnDef<Testimonial>[] = [\r\n    {\r\n      accessorKey: \"class\",\r\n      header: \"Class\",\r\n      cell: ({ row }) => {\r\n        const classData = row.original.class;\r\n        const logo = classData.classesLogo || classData.ClassAbout?.classesLogo;\r\n        const apiBaseUrl =\r\n          process.env.NEXT_PUBLIC_API_BASE_URL || \"http://localhost:4005\";\r\n\r\n        return (\r\n          <div className=\"flex items-center gap-3\">\r\n            {logo ? (\r\n              <div className=\"relative w-10 h-10 rounded-full overflow-hidden border\">\r\n                <Image\r\n                  src={`${apiBaseUrl}${logo}`}\r\n                  alt=\"Logo\"\r\n                  fill\r\n                  className=\"object-cover\"\r\n                />\r\n              </div>\r\n            ) : (\r\n              <div className=\"w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-500\">\r\n                {classData.firstName?.[0] || \"C\"}\r\n              </div>\r\n            )}\r\n            <div>\r\n              <div className=\"font-medium\">{classData.className || \"N/A\"}</div>\r\n              <div className=\"text-xs text-gray-500\">\r\n                {classData.fullName ||\r\n                  `${classData.firstName} ${classData.lastName}`}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        );\r\n      },\r\n    },\r\n    {\r\n      accessorKey: \"rating\",\r\n      header: \"Rating\",\r\n      cell: ({ row }) => renderStars(row.original.rating),\r\n    },\r\n    {\r\n      accessorKey: \"message\",\r\n      header: \"Testimonial\",\r\n      cell: ({ row }) => {\r\n        const msg = row.original.message;\r\n        const isLong = msg.length > 80;\r\n        return (\r\n          <TooltipProvider>\r\n            <Tooltip>\r\n              <TooltipTrigger asChild>\r\n                <span className=\"cursor-pointer max-w-md\">\r\n                  {isLong ? `${msg.slice(0, 50)}...` : msg}\r\n                </span>\r\n              </TooltipTrigger>\r\n              <TooltipContent\r\n                className=\"max-w-[300px] p-3 bg-slate-200 text-black rounded-lg shadow-md border border-gray-600 text-sm leading-relaxed whitespace-normal break-words\"\r\n                side=\"bottom\"\r\n              >\r\n                <p>{msg}</p>\r\n              </TooltipContent>\r\n            </Tooltip>\r\n          </TooltipProvider>\r\n        );\r\n      },\r\n    },\r\n    {\r\n      accessorKey: \"status\",\r\n      header: \"Status\",\r\n      cell: ({ row }) => (\r\n        <Select\r\n          value={row.original.status}\r\n          onValueChange={(value: \"PENDING\" | \"APPROVED\" | \"REJECTED\") =>\r\n            handleStatusChange(row.original.id, value)\r\n          }\r\n          disabled={updatingTestimonialId === row.original.id}\r\n        >\r\n          <SelectTrigger\r\n            className=\"w-[150px] mb-3\"\r\n            aria-label={`Status for testimonial ${row.original.id}`}\r\n          >\r\n            {updatingTestimonialId === row.original.id ? (\r\n              <Loader2 className=\"h-4 w-4 animate-spin\" />\r\n            ) : (\r\n              <SelectValue placeholder=\"Select status\" />\r\n            )}\r\n          </SelectTrigger>\r\n          <SelectContent>\r\n            <SelectItem value=\"PENDING\">Pending</SelectItem>\r\n            <SelectItem value=\"APPROVED\">Approved</SelectItem>\r\n            <SelectItem value=\"REJECTED\">Rejected</SelectItem>\r\n          </SelectContent>\r\n        </Select>\r\n      ),\r\n    },\r\n    {\r\n      accessorKey: \"createdAt\",\r\n      header: \"Submitted At\",\r\n      cell: ({ row }) => new Date(row.original.createdAt).toLocaleString(),\r\n    },\r\n    {\r\n      id: \"actions\",\r\n      header: \"Actions\",\r\n      cell: ({ row }) => (\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"icon\"\r\n          onClick={() => openDeleteConfirmation(row.original.id)}\r\n          className=\"text-red-500 hover:text-red-700 hover:bg-red-100\"\r\n        >\r\n          <Trash2 className=\"h-4 w-4\" />\r\n          <span className=\"sr-only\">Delete</span>\r\n        </Button>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"flex justify-between items-center mx-2 mb-2\">\r\n        <h1 className=\"text-2xl font-bold ms-2\">Classes Testimonials</h1>\r\n        <div className=\"flex gap-4\">\r\n          <Select\r\n            value={statusFilter}\r\n            onValueChange={(\r\n              value: \"ALL\" | \"PENDING\" | \"APPROVED\" | \"REJECTED\"\r\n            ) => setStatusFilter(value)}\r\n          >\r\n            <SelectTrigger className=\"w-[150px]\">\r\n              <SelectValue placeholder=\"Filter by status\" />\r\n            </SelectTrigger>\r\n            <SelectContent>\r\n              <SelectItem value=\"ALL\">All</SelectItem>\r\n              <SelectItem value=\"PENDING\">Pending</SelectItem>\r\n              <SelectItem value=\"APPROVED\">Approved</SelectItem>\r\n              <SelectItem value=\"REJECTED\">Rejected</SelectItem>\r\n            </SelectContent>\r\n          </Select>\r\n          <div className=\"mt-1 flex items-center space-x-5\">\r\n            <Button onClick={handleFilter}>Filter</Button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <DataTable\r\n        columns={columns}\r\n        data={testimonials}\r\n        isLoading={isLoading}\r\n      />\r\n\r\n      <Pagination\r\n        page={currentPage}\r\n        totalPages={totalPages}\r\n        setPage={handlePageChange}\r\n        entriesText={`${testimonials.length} entries`}\r\n      />\r\n\r\n      <ConfirmDialog\r\n        open={isDeleteDialogOpen}\r\n        setOpen={setIsDeleteDialogOpen}\r\n        title=\"Are you sure?\"\r\n        description=\"This will permanently delete the testimonial.\"\r\n        confirmText=\"Delete\"\r\n        cancelText=\"Cancel\"\r\n        onConfirm={handleDeleteTestimonial}\r\n        isLoading={isDeleting}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TestimonialTable;\r\n"], "names": [], "mappings": ";;;AAiLU;;AA/KV;AAEA;AACA;AAOA;AAMA;AAAA;AAAA;AACA;AACA;AACA;AAEA;AAKA;AACA;;;AA7BA;;;;;;;;;;;;AA+BA,MAAM,YAAY;AAElB,MAAM,mBAAmB;;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAE/D;IACF,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC3D;IAEF,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAE7C;IAEF,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAClC,OACE,MACA;YAEA,IAAI;gBACF,aAAa;gBACb,MAAM,eACJ,UAAU,WAAW,QAChB,SACD;gBACN,MAAM,WAAW,MAAM,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,WAAW;gBAExD,gBAAgB,SAAS,YAAY,IAAI,EAAE;gBAC3C,cAAc,SAAS,KAAK,IAAI;YAClC,EAAE,OAAO,OAAY;gBACnB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;gBAC7B,gBAAgB,EAAE;gBAClB,cAAc;YAChB,SAAU;gBACR,aAAa;YACf;QACF;0DACA,EAAE;IAGJ,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,kBAAkB;QACpB;qCAAG;QAAC;KAAkB;IAEtB,MAAM,eAAe;QACnB,eAAe;QACf,kBAAkB,GAAG;IACvB;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,QAAQ,KAAK,QAAQ,YAAY;YACnC,eAAe;YACf,kBAAkB,MAAM;QAC1B;IACF;IAEA,MAAM,qBAAqB,OACzB,eACA;QAEA,MAAM,uBAAuB;eAAI;SAAa;QAE9C,IAAI;YACF,yBAAyB;YAEzB,gBACE,aAAa,GAAG,CAAC,CAAC,IAChB,EAAE,EAAE,KAAK,gBACL;oBAAE,GAAG,CAAC;oBAAE;oBAAQ,WAAW,IAAI,OAAO,WAAW;gBAAG,IACpD;YAIR,MAAM,qBAAqB,MAAM,CAAA,GAAA,oIAAA,CAAA,0BAAuB,AAAD,EACrD,eACA;YAGF,IAAI,oBAAoB;gBACtB,gBACE,aAAa,GAAG,CAAC,CAAC,IAChB,EAAE,EAAE,KAAK,gBACL;wBAAE,GAAG,kBAAkB;wBAAE,OAAO,EAAE,KAAK;oBAAC,IACxC;gBAGR,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,8BAA8B,EAAE,QAAQ;YACzD;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,sCAAsC;YACpD,gBAAgB;YAChB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;QAC/B,SAAU;YACR,yBAAyB;QAC3B;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,uBAAuB;QACvB,sBAAsB;IACxB;IAEA,MAAM,0BAA0B;QAC9B,IAAI,CAAC,qBAAqB;QAE1B,cAAc,OAAO,eAAe;QAEpC,IAAI;YACF,MAAM,CAAA,GAAA,oIAAA,CAAA,oBAAiB,AAAD,EAAE;YACxB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,kBAAkB;QACpB,EAAE,OAAO,OAAY;YACnB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;QAC/B,SAAU;YACR,cAAc,QAAQ,cAAc;YACpC,sBAAsB;YACtB,uBAAuB;QACzB;IACF;IAEA,MAAM,cAAc,CAAC,uBACnB,6LAAC;YAAI,WAAU;sBACZ;mBAAI,MAAM;aAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,qMAAA,CAAA,OAAI;oBAEH,WAAW,CAAC,QAAQ,EAClB,IAAI,SAAS,oCAAoC,iBACjD;mBAHG;;;;;;;;;;IASb,MAAM,UAAoC;QACxC;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,YAAY,IAAI,QAAQ,CAAC,KAAK;gBACpC,MAAM,OAAO,UAAU,WAAW,IAAI,UAAU,UAAU,EAAE;gBAC5D,MAAM,aACJ,8DAAwC;gBAE1C,qBACE,6LAAC;oBAAI,WAAU;;wBACZ,qBACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAK,GAAG,aAAa,MAAM;gCAC3B,KAAI;gCACJ,IAAI;gCACJ,WAAU;;;;;;;;;;iDAId,6LAAC;4BAAI,WAAU;sCACZ,UAAU,SAAS,EAAE,CAAC,EAAE,IAAI;;;;;;sCAGjC,6LAAC;;8CACC,6LAAC;oCAAI,WAAU;8CAAe,UAAU,SAAS,IAAI;;;;;;8CACrD,6LAAC;oCAAI,WAAU;8CACZ,UAAU,QAAQ,IACjB,GAAG,UAAU,SAAS,CAAC,CAAC,EAAE,UAAU,QAAQ,EAAE;;;;;;;;;;;;;;;;;;YAK1D;QACF;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,GAAK,YAAY,IAAI,QAAQ,CAAC,MAAM;QACpD;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,MAAM,IAAI,QAAQ,CAAC,OAAO;gBAChC,MAAM,SAAS,IAAI,MAAM,GAAG;gBAC5B,qBACE,6LAAC,sIAAA,CAAA,kBAAe;8BACd,cAAA,6LAAC,sIAAA,CAAA,UAAO;;0CACN,6LAAC,sIAAA,CAAA,iBAAc;gCAAC,OAAO;0CACrB,cAAA,6LAAC;oCAAK,WAAU;8CACb,SAAS,GAAG,IAAI,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;;;;;;;;;;;0CAGzC,6LAAC,sIAAA,CAAA,iBAAc;gCACb,WAAU;gCACV,MAAK;0CAEL,cAAA,6LAAC;8CAAG;;;;;;;;;;;;;;;;;;;;;;YAKd;QACF;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,6LAAC,qIAAA,CAAA,SAAM;oBACL,OAAO,IAAI,QAAQ,CAAC,MAAM;oBAC1B,eAAe,CAAC,QACd,mBAAmB,IAAI,QAAQ,CAAC,EAAE,EAAE;oBAEtC,UAAU,0BAA0B,IAAI,QAAQ,CAAC,EAAE;;sCAEnD,6LAAC,qIAAA,CAAA,gBAAa;4BACZ,WAAU;4BACV,cAAY,CAAC,uBAAuB,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE;sCAEtD,0BAA0B,IAAI,QAAQ,CAAC,EAAE,iBACxC,6LAAC,oNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;qDAEnB,6LAAC,qIAAA,CAAA,cAAW;gCAAC,aAAY;;;;;;;;;;;sCAG7B,6LAAC,qIAAA,CAAA,gBAAa;;8CACZ,6LAAC,qIAAA,CAAA,aAAU;oCAAC,OAAM;8CAAU;;;;;;8CAC5B,6LAAC,qIAAA,CAAA,aAAU;oCAAC,OAAM;8CAAW;;;;;;8CAC7B,6LAAC,qIAAA,CAAA,aAAU;oCAAC,OAAM;8CAAW;;;;;;;;;;;;;;;;;;QAIrC;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,GAAK,IAAI,KAAK,IAAI,QAAQ,CAAC,SAAS,EAAE,cAAc;QACpE;QACA;YACE,IAAI;YACJ,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,uBAAuB,IAAI,QAAQ,CAAC,EAAE;oBACrD,WAAU;;sCAEV,6LAAC,6MAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;QAGhC;KACD;IAED,qBACE,6LAAC;;0BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,OAAO;gCACP,eAAe,CACb,QACG,gBAAgB;;kDAErB,6LAAC,qIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,6LAAC,qIAAA,CAAA,gBAAa;;0DACZ,6LAAC,qIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAM;;;;;;0DACxB,6LAAC,qIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAU;;;;;;0DAC5B,6LAAC,qIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAW;;;;;;0DAC7B,6LAAC,qIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAW;;;;;;;;;;;;;;;;;;0CAGjC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;8CAAc;;;;;;;;;;;;;;;;;;;;;;;0BAKrC,6LAAC,yIAAA,CAAA,YAAS;gBACR,SAAS;gBACT,MAAM;gBACN,WAAW;;;;;;0BAGb,6LAAC,0IAAA,CAAA,UAAU;gBACT,MAAM;gBACN,YAAY;gBACZ,SAAS;gBACT,aAAa,GAAG,aAAa,MAAM,CAAC,QAAQ,CAAC;;;;;;0BAG/C,6LAAC,6IAAA,CAAA,UAAa;gBACZ,MAAM;gBACN,SAAS;gBACT,OAAM;gBACN,aAAY;gBACZ,aAAY;gBACZ,YAAW;gBACX,WAAW;gBACX,WAAW;;;;;;;;;;;;AAInB;GAlTM;KAAA;uCAoTS", "debugId": null}}, {"offset": {"line": 1324, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/app/%28dashboard%29/testimonials/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport TestimonialTable from \"@/components/TestimonialTable\";\r\n\r\n\r\nexport default function TestimonialsPage() {\r\n  return (\r\n    <div className=\"flex flex-1 flex-col gap-4 p-4 pt-0\">\r\n      <TestimonialTable />\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAKe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,yIAAA,CAAA,UAAgB;;;;;;;;;;AAGvB;KANwB", "debugId": null}}]}