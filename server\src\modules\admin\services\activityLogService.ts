import prisma from '@/config/prismaClient';
import { UserType } from '@prisma/client';

interface GetActivityLogsParams {
  page: number;
  limit: number;
  userType?: string;
  activityType?: string;
  search?: string;
}

export const getActivityLogs = async (params: GetActivityLogsParams) => {
  const { page, limit, userType, activityType, search } = params;
  const skip = (page - 1) * limit;

  const where: any = {};

  if (userType && Object.values(UserType).includes(userType as UserType)) {
    where.userType = userType as UserType;
  }

  if (activityType) {
    where.activityType = activityType;
  }

  const [logs, total] = await Promise.all([
    prisma.userActivityLog.findMany({
      where,
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' },
    }),
    prisma.userActivityLog.count({ where }),
  ]);

  const enrichedLogs = await Promise.all(
    logs.map(async (log) => {
      let userDetails = null;

      if (log.userType === UserType.STUDENT) {
        userDetails = await prisma.student.findUnique({
          where: { id: log.userId },
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            contact: true,
          },
        });
      } else if (log.userType === UserType.CLASS) {
        userDetails = await prisma.classes.findUnique({
          where: { id: log.userId },
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            contactNo: true,
            className: true,
          },
        });
      }

      return {
        ...log,
        userDetails,
      };
    })
  );

  let filteredLogs = enrichedLogs;
  if (search) {
    const searchLower = search.toLowerCase();
    filteredLogs = enrichedLogs.filter((log) => {
      const userDetails = log.userDetails;
      if (!userDetails) return false;

      const fullName = `${userDetails.firstName} ${userDetails.lastName}`.toLowerCase();
      const email = userDetails.email?.toLowerCase() || '';

      let contact = '';
      if (log.userType === UserType.STUDENT && 'contact' in userDetails) {
        contact = userDetails.contact || '';
      } else if (log.userType === UserType.CLASS && 'contactNo' in userDetails) {
        contact = userDetails.contactNo || '';
      }

      return fullName.includes(searchLower) ||
        email.includes(searchLower) ||
        contact.toLowerCase().includes(searchLower);
    });
  }

  return {
    logs: search ? filteredLogs : enrichedLogs,
    pagination: {
      page,
      limit,
      total: search ? filteredLogs.length : total,
      totalPages: search ? Math.ceil(filteredLogs.length / limit) : Math.ceil(total / limit),
    },
  };
};
