@include('auth.header')

<body class="hold-transition login-page">
    
    <div class="container-fluid">
        <div class="login d-flex align-items-center py-5">
            <div class="container">
                <div class="row">
                    <div class="col-lg-4 col-xl-4 mx-auto">
                        <div class="content">
                            <img src="{{ $tenantLogo }}" alt="logo" class="mb-4"
                                style="width:170px;" />
                            <p class="text-muted mb-4">Sign In to Continue</p>
                        </div>
                        <form method="POST" action="{{ route('login') }}">
                            @csrf
                            <div class="form-group mb-3">
                                <input id="inputEmail" type="email" name="email" placeholder="Email address"
                                    required="" value="{{ old('email') }}"
                                    class="form-control  @error('email') is-invalid @enderror shadow-sm px-4">
                                @error('email')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                            <div class="form-group mb-3">
                                <input id="inputPassword" type="password" name="password" placeholder="Password"
                                    required=""
                                    class="form-control shadow-sm px-4 text-primary  @error('password') is-invalid @enderror">
                                @error('password')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                            <button type="submit" class="btn btn-primary btn-block text-uppercase mb-2 shadow-sm">
                                {{ __('Login') }}</button>
                        </form>
                    </div>
                    <div class="col-md-12 text-center">
                        @if (Route::has('password.request'))
                            <a class="btn btn-link" href="{{ route('password.request') }}">
                                {{ __('Forgot Your Password?') }}
                            </a>
                        @endif
                    </div>
                    <div class="bottom-copyright">
                        <p class="text-muted mb-4"><strong>Copyright ©
                                <a href="https://www.uest.in/">Uest</a>.
                            </strong> All rights reserved. </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>
