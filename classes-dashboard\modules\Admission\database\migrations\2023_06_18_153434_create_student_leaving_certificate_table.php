<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStudentLeavingCertificateTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('student_leaving_certificate', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('student_id')->nullable();
            $table->foreign('student_id')->references('id')->on('student_details')->onDelete('cascade');
            $table->string('student_name');
            $table->string('mothers_name');
            $table->string('place_of_birth');
            $table->string('nationality');
            $table->string('category');
            $table->date('date_of_birth');
            $table->string('last_school_attained');
            $table->date('date_of_admission');
            $table->string('admission_standard');
            $table->date('date_of_leaving_school');
            $table->string('standard');
            $table->string('reason_for_leaving');
            $table->integer('attendance');
            $table->string('progress');
            $table->string('conduct');
            $table->string('remarks');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('student_leaving_certificate');
    }
}
