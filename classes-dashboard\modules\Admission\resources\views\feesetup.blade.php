@extends('layouts.app')
@section('content')
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-12 main-title-flex">
                    <h1>Student Fee Setup</h1>
                </div>
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </div>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="card card-primary">
                    <div class="card-header">
                        <h3 class="card-title" style="color:white !important">Student Details</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <label class="col-form-label"><b>Student Name : </b> {{ $studentdata->first_name }}
                                {{ $studentdata->last_name }}</label>
                            <input type="hidden" id="student-data" value="{{ $studentdata->id }}" />
                        </div>
                        <div class="row">
                            <label class="col-form-label"><b>Classroom : </b>
                                {{ $studentdata->getAcademicInfo->getClassroom->class_name }}
                                ({{ $studentdata->getAcademicInfo->getDepartment->name }})</label>
                            <input type="hidden" id="department"
                                value="{{ $studentdata->getAcademicInfo->getDepartment->id }}" />
                            <input type="hidden" id="classroom-filter"
                                value="{{ $studentdata->getAcademicInfo->getClassroom->id }}" />

                        </div>
                        <div class="row">
                            <label class="col-form-label"><b>Year : </b>
                                {{ $studentdata->getAcademicInfo->getYear->year_name }}</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <div class="card card-primary" id="fees-structure">
                    <div class="card-header">
                        <h3 class="card-title" style="color:white !important">Fees Structure</h3>
                    </div>
                    <div class="card-body">
                        <div class="generate-buttons">
                            @can('export student fee data')
                                <button class="btn btn-dark exportDetailedFees"><i class="fa fa-file-excel"></i>&nbsp;
                                    Export
                                </button>
                            @endcan
                        </div>
                        <div class="table-responsive">
                            <input type="hidden" name="year" id="year" />
                            <div class="table-responsive">
                                @php
                                    use Carbon\Carbon;
                                    $year = geActiveYearName();
                                    $start = Carbon::parse($year->start_date);
                                    $end = Carbon::parse($year->end_date);
                                    $months = [];

                                    while ($start->lte($end)) {
                                        $months[] = $start->format('F-Y'); // e.g. January-2025
                                        $start->addMonth();
                                    }
                                @endphp
                                <table id="classroomWiseFee_table" class="table display  table-striped  table-borderless">
                                    <thead>
                                        <tr>
                                            <th style="min-width:150px !important" class="bg-info">Pay</th>
                                            <th class="bg-info">Month Name</th>
                                            @if (isset($feesdetail))
                                                @foreach ($feesdetail as $installment)
                                                    @php
                                                        $categoryData = collect(
                                                            $installment['installment_data'],
                                                        )->mapWithKeys(function ($item) {
                                                            return [$item['fees_category_id'] => $item];
                                                        });
                                                    @endphp
                                                @endforeach
                                            @endif

                                            @if (isset($categoryData))
                                                @foreach ($categoryData as $cat)
                                                    <th style="min-width:150px !important" class="bg-info">
                                                        {{ $cat['fees_category'] }}
                                                    </th>
                                                @endforeach
                                            @endif
                                            <th style="min-width:150px !important" class="bg-info">Total Amount</th>
                                            <th style="min-width:150px !important" class="bg-info">Total Discount</th>
                                            <th style="min-width:150px !important" class="bg-info">Total Paid Amount</th>
                                            <th style="min-width:150px !important" class="bg-info">Due Amount</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @if (isset($feesdetail))
                                            @foreach ($feesdetail as $installment)
                                                @foreach ($months as $month)
                                                    <tr id="row"
                                                        style="{{ $installment['due_amount'] == 0 ? 'background-color : darkseagreen' : '' }}">
                                                        <td>
                                                            @if (!isset($installment) || $installment['fee_locked'] == 0)
                                                                <div class="alert alert-warning">
                                                                    <i class="fas fa-exclamation-triangle"></i> Fee is not
                                                                    locked yet.
                                                                </div>
                                                            @else
                                                                <button data-studentid="{{ $studentdata->id }}"
                                                                    data-toggle="modal" data-target="#makePayment"
                                                                    class="btn btn-primary payBtn"
                                                                    data-month="{{ $month }}"
                                                                    data-fees='@json($installment["installment_data"])'
                                                                    style="{{ $installment['due_amount'] == 0 ? 'display : none' : '' }}">
                                                                    <i class="fas fa-save"></i> Pay Now
                                                                </button>
                                                            @endif
                                                            @if ($installment['due_amount'] == 0)
                                                                <button class="btn btn-secondary">
                                                                    <i class="fas fa-lock"></i> Paid
                                                                </button>
                                                            @endif
                                                        </td>
                                                        <td>
                                                            {{ $month }}
                                                        </td>
                                                        @foreach ($installment['installment_data'] as $idc)
                                                            <td class="sumcol">
                                                                <input type="number" readonly
                                                                    value="{{ $idc['amount'] ?? 0 }}"
                                                                    name="category_{{ $idc['fees_category_id'] }}[]"
                                                                    required max="9999999" placeholder="Add Fee"
                                                                    class="form-control total category category_{{ $idc['fees_category_id'] }}"
                                                                    data-category="{{ $idc['fees_category'] }}" />
                                                            </td>
                                                        @endforeach
                                                        <td>
                                                            <input type="number" readonly
                                                                value="{{ $installment['total_amount'] }}"
                                                                data-category="Total Amount" class="form-control" />
                                                        </td>
                                                        <td>
                                                            <input type="number" readonly
                                                                value="{{ $installment['discount_amount'] }}"
                                                                data-category="Discount Amount"
                                                                class="form-control discountamount category" />
                                                        </td>
                                                        <td>
                                                            <input type="number" readonly
                                                                value="{{ $installment['paid_amount'] ?? 0 }}"
                                                                data-category="Paid Amount" class="form-control" />
                                                        </td>
                                                        <td class="dueamount">
                                                            <input type="number" readonly
                                                                value="{{ $installment['due_amount'] }}" name="dueamount"
                                                                placeholder="Due Amount" class="form-control" />
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            @endforeach
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="float-right mt-10">
                            <h5 class="totalfees">Total Fees :
                                {{ array_sum(array_column($feesdetail, 'total_amount')) * 12 - array_sum(array_column($feesdetail, 'discount_amount')) }}
                                <h5>
                                    <h5 class="totalduefees">Total Due Fees :
                                        {{ array_sum(array_column($feesdetail, 'due_amount')) * 12 }}<h5>
                                            <h5 class="totalpaidfees">Total Paid Fees :
                                                {{ array_sum(array_column($feesdetail, 'paid_amount')) }}<h5>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <div class="card card-primary">
                    <div class="card-header">
                        <h3 class="card-title" style="color:white !important">Payment Details</h3>
                    </div>
                    <div class="card-body">
                        <div class="generate-buttons">
                            @can('export student fee data')
                                <button class="btn btn-dark exportData"><i class="fa fa-file-excel"></i>&nbsp;
                                    Payment Data Export
                                </button>
                            @endcan
                        </div>
                        <table id="payment_details" class="table display  table-striped  table-borderless dt-responsive">
                            <thead>
                                <tr>
                                    <th class="bg-info">Action</th>
                                    <th class="bg-info">Student Name</th>
                                    <th class="bg-info">Classroom</th>
                                    <th class="bg-info">Payment Date</th>
                                    <th class="bg-info">Installment</th>
                                    <th class="bg-info">Payment Mode</th>
                                    <th class="bg-info">Paid Amount</th>
                                    <th class="bg-info">Cheque No</th>
                                    <th class="bg-info">Reference No</th>
                                    <th class="bg-info">Payment Status</th>
                                    <th class="bg-info">Transaction Id</th>
                                </tr>
                            </thead>
                            <tfoot>
                                <tr class="search-row">
                                    <th>Action</th>
                                    <th>Student Name</th>
                                    <th>Classroom</th>
                                    <th>Payment Date</th>
                                    <th>Installment</th>
                                    <th>Payment Mode</th>
                                    <th>Paid Amount</th>
                                    <th>Cheque No</th>
                                    <th>Reference No</th>
                                    <th>Payment Status</th>
                                    <th>Transaction Id</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </section>
    @include('Admission::paymentform')
@endsection
@section('scripts')
    <script>
        var feesRoute = {
            index: "{{ route('getAllStudentPaymentLogs') }}",
            delete: "{{ route('deletePaymentLogs', ':payid') }}",
            payStudentFee: "{{ route('payStudentFee') }}",
            paidFee: "{{ route('getPaidFees', ['installment_name' => ':installment_name', 'student_id' => $studentdata->id]) }}",
            feeDiscount: "{{ route('feesDiscount', $studentdata->id) }}",
            exportDetailed: "{{ route('export-detailed-fees') }}",
            export: "{{ route('export-feesPayment') }}",
        };
    </script>
    <script src="{{ asset(mix('js/page-level-js/Admission/js/feesetup.js')) }}"></script>
@endsection
