<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Support\Facades\Auth;
use App\Models\Classes;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Http\Request;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = RouteServiceProvider::HOME;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }

    public function loginClassViaToken(Request $request)
    {
        $uid = $request->query('uid');
        $token = $request->query('token');

        if (!$uid || !$token) {
            return abort(403, 'Missing UID or Token');
        }

        try {
            $decoded = JWT::decode($token, new Key('secret123', 'HS256'));
            if ((string) $decoded->id !== (string) $uid) {
                return abort(403, 'Token mismatch');
            }

            $classUser = Classes::findOrFail($uid);
            if (!$classUser) {
                return abort(403, 'User not found');
            }

            // Log in the user
            Auth::guard('class_users')->login($classUser); // Remember the user

            // Save session explicitly
            $request->session()->put('class_users_id', $classUser->id);
            $request->session()->save();

            return redirect()->route('home');
        } catch (\Exception $e) {
            return abort(403, 'Authentication failed: ' . $e->getMessage());
        }
    }
}
