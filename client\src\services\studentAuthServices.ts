import { axiosInstance } from '@/lib/axios';
import { GoogleAuthData } from '@/lib/types';

interface StudentRegisterData {
  firstName: string;
  lastName: string;
  contactNo: string;
  referralCode?: string;
}

interface StudentLoginData {
  contactNo: string;
  email?: string;
}

interface VerifyOtpData {
  contactNo: string;
  otp: string;
  firstName?: string;
  lastName?: string;
  email?: string;
}

interface ResendOtpData {
  contactNo: string;
  firstName?: string;
}

interface ContinueWithEmailData {
  email: string;
}

export const continueWithEmail = async (data: ContinueWithEmailData) => {
  const response = await axiosInstance.post('/student/continue-with-email', data);
  return response.data;
};

export const registerStudent = async (data: StudentRegisterData) => {
  const response = await axiosInstance.post('/student/register', data);
  return response.data;
};

export const loginStudent = async (data: StudentLoginData) => {
  const response = await axiosInstance.post('/student/login', data);
  return response.data;
};

export const logoutStudent = async (): Promise<any> => {
  try {
    // Call server logout endpoint to clear cookie
    const response = await axiosInstance.post('/student/logout');

    // Clear local storage regardless of server response
    localStorage.removeItem('studentToken');
    localStorage.removeItem('student_data');

    return response.data;
  } catch (error) {
    // Even if server call fails, clear local storage
    localStorage.removeItem('studentToken');
    localStorage.removeItem('student_data');

    return {
      success: true,
      message: 'Logged out successfully',
    };
  }
};

export async function verifyOtp(data: VerifyOtpData) {
  const response = await axiosInstance.post('/student/verify-otp', data);
  return response.data;
}

export async function resendOtp(data: ResendOtpData) {
  const response = await axiosInstance.post('/student/resend-otp', data);
  return response.data;
}

export const googleAuthStudent = async (googleAuthData: GoogleAuthData) => {
    const response = await axiosInstance.post(`/student/google-auth`, googleAuthData);
    return response.data;
};

export const studentverifyEmail = async (token: string) => {
    const response = await axiosInstance.post(`/student/verify-email`, { token });
    return response.data;
};

export const studentresendVerificationEmail = async (email: string) => {
    const response = await axiosInstance.post(`/student/resend-verification`, { email });
    return response.data;
};