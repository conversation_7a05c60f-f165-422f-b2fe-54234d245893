import { Router } from 'express';
import {
  getAllStudentController,
  getAllStudentCountsController,
  getCountStudent,
  getCurrentStudentController,
  updateStudentController,
  registerStudent,
  loginStudent,
  resendOtp,
  verifyOtpController,
  continueWithEmail,
  logout
} from '../controllers/studentAuthController';
import { studentAuthMiddleware } from '@/middlewares/studentAuth';
import { authMiddleware } from '@/middlewares/adminAuth';
import validateRequest from '@/middlewares/validateRequest';
import { continueWithEmailSchema, loginSchema, registerSchema, resendOtpSchema, verifyOtpSchema } from '../requests/authRequest'

const studentAuthRouter = Router();

studentAuthRouter.post('/register', validateRequest(registerSchema), registerStudent);
studentAuthRouter.post('/continue-with-email', validateRequest(continueWithEmailSchema), continueWithEmail);
studentAuthRouter.post('/login', validateRequest(loginSchema), loginStudent);
studentAuthRouter.post('/verify-otp', validateRequest(verifyOtpSchema), verifyOtpController);
studentAuthRouter.post('/resend-otp', validateRequest(resendOtpSchema), resendOtp);
studentAuthRouter.post('/logout', logout);


// studentAuthRouter.post('/google-auth', googleAuthStudent);
// studentAuthRouter.post('/verify-email', verifyEmail);
// studentAuthRouter.post('/resend-verification', resendVerificationEmail);


// Student data endpoints
studentAuthRouter.get('/', authMiddleware,getAllStudentController);
studentAuthRouter.get('/count', getCountStudent);
studentAuthRouter.get('/me', studentAuthMiddleware, getCurrentStudentController);
studentAuthRouter.put('/update', studentAuthMiddleware, updateStudentController);
studentAuthRouter.get('/counts', getAllStudentCountsController);

export default studentAuthRouter;