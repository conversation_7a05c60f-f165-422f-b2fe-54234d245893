<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStudentParentsDetailsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('student_parents_details', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('student_id')->nullable();
            $table->foreign('student_id')->references('id')->on('student_details')->onDelete('cascade');
            $table->string('fathers_name')->nullable();
            $table->string('fathers_middle_name')->nullable();
            $table->string('fathers_last_name')->nullable();
            $table->biginteger('fathers_aadhaar_no')->nullable();
            $table->string('fathers_qualification')->nullable();
            $table->string('fathers_occupation')->nullable();
            $table->string('mothers_name')->nullable();
            $table->string('mothers_middle_name')->nullable();
            $table->string('mothers_last_name')->nullable();
            $table->biginteger('mothers_aadhaar_no')->nullable();
            $table->string('mothers_qualification')->nullable();
            $table->string('mothers_occupation')->nullable();
            $table->biginteger('contact_no_1')->nullable();
            $table->biginteger('contact_no_2')->nullable();
            $table->string('family_income')->nullable();
            $table->string('part_of_ngo')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('student_parents_details');
    }
}
