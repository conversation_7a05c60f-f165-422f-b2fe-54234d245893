<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class CreateStudentDetailsViewTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement("DROP VIEW IF EXISTS student_details_view");
        DB::statement("
            CREATE VIEW student_details_view AS
            SELECT
            student_details.*,
            department.name AS department_name,
            department.id AS department_id,
            classrooms.class_name,
            classrooms.id AS classroom_id,
            years.year_name AS year_name,
            years.id AS year_id,
            student_academic_info.status,
            student_details.class_uuid as student_class_uuid
            FROM
            student_details
                INNER JOIN student_academic_info ON student_details.id = student_academic_info.student_id
                LEFT JOIN department ON student_academic_info.department = department.id
                LEFT JOIN classrooms ON student_academic_info.classroom = classrooms.id
                LEFT JOIN years ON student_academic_info.year = years.id
            WHERE student_details.deleted_at IS NULL;
        ");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement("DROP VIEW IF EXISTS student_details_view");
    }
}
