<?php

namespace Fees\Repositories;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Admission\Models\StudentFeesDiscount;
use Admission\Repositories\AdmissionRepository;
use Fees\Interfaces\StudentFeePaymentInterface;
use Fees\Models\ClassroomFeesDetails;
use Fees\Models\InstallmentDetails;
use Fees\Models\FeesView;
use Fees\Models\StudentPayments;

class StudentFeePaymentRepository implements StudentFeePaymentInterface
{
    protected $studentPayments;
    protected $studentFeesDiscount;
    protected $feesView;
    protected $admissionRepository;

    public function __construct(
        StudentPayments $studentPayments,
        StudentFeesDiscount $studentFeesDiscount,
        FeesView $feesView,
        AdmissionRepository $admissionRepository
    ) {
        $this->studentPayments = $studentPayments;
        $this->studentFeesDiscount = $studentFeesDiscount;
        $this->feesView = $feesView;
        $this->admissionRepository = $admissionRepository;
    }

    public function payStudentFee($data)
    {
        $data['payment_status'] = config('constants.PAYMENT_STATUS.SUCCESS');
        $data['payment_date'] = date('Y-m-d');
        $data['taken_by'] = Auth::id();
        $this->studentPayments::create($data);
    }

    public function getAll($data)
    {
        $paymentLogs =  $this->studentPayments
            ->leftJoin('student_details_view', 'student_details_view.id', '=', 'student_payment_details.student_id')

            ->when(!empty($data['start_date']), function ($query) use ($data) {
                return $query->whereDate('payment_date', '>=', $data['start_date']);
            })
            ->when(!empty($data['end_date']), function ($query) use ($data) {
                return $query->whereDate('payment_date', '<=', $data['end_date']);
            })
            ->when(!empty($data['department']), function ($query) use ($data) {
                return $query->where('student_details_view.department_id', $data['department']);
            })
            ->when(!empty($data['classroom']), function ($query) use ($data) {
                return $query->where('student_details_view.classroom_id', $data['classroom']);
            })
            ->when(!empty($data['student_id']), function ($query) use ($data) {
                return $query->where('student_id', $data['student_id']);
            })->where('student_details_view.year_id', getActiveYearId())
            ->select(
                'student_details_view.first_name',
                'student_details_view.last_name',
                'student_details_view.classroom_id as student_class_id',
                'student_details_view.department_id as student_department_id',
                'student_payment_details.*',
                DB::raw("CONCAT(student_details_view.first_name, ' ', student_details_view.last_name) AS student_full_name"),
                DB::raw("CONCAT(student_details_view.class_name, ' (', student_details_view.department_name, ')') AS student_class_info"),
            );

        $concatenatedColumns = ['student_full_name', 'student_class_info'];
        searchColumn($data->input('columns'), $paymentLogs,  $concatenatedColumns);
        orderColumn($data, $paymentLogs, 'student_payment_details.id');

        return $paymentLogs;
    }

    public function getStudentPaymentDatatable($list)
    {
        $totalCreditAmount = number_format($list->sum('paid_amount'), 2, '.', ',');

        return datatables()->of($list)
            ->addColumn('student_full_name', function ($data) {
                return "<a href=" . route('studentfee', $data->student_id) . ">" . $data->student_full_name . "</a>";
            })
            ->addColumn('student_class_info', function ($data) {
                return  $data->student_class_info;
            })
            ->addColumn('month_name', function ($data) {
                return $data->month_name;
            })
            ->addColumn('payment_date', function ($data) {
                return $data->payment_date;
            })
            ->addColumn('payment_mode', function ($data) {
                return $data->payment_mode;
            })
            ->addColumn('paid_amount', function ($data) {
                return $data->paid_amount;
            })
            ->addColumn('cheque_no', function ($data) {
                return ($data->cheque_no != "") ? $data->cheque_no : "N/A";
            })
            ->addColumn('reference_no', function ($data) {
                return ($data->reference_no != "") ? $data->reference_no : "N/A";
            })
            ->addColumn('payment_status', function ($data) {
                return payment_color($data->payment_status);
            })
            ->addColumn('transaction_id', function ($data) {
                return ($data->transaction_id != "") ? $data->transaction_id : "N/A";
            })
            ->addColumn('action', function ($data) {
                $button = '';
                $button .= '<a href="' . route('studentPaymentReceipt', $data->id) . '" class="btn">
                <i class="fas fa-eye"></i></a>';
                if (Auth::user()->getIsSuperAdmin()) {
                    $button .= '<button data-paymentID=' . $data->id . ' class="btn deletePayment">
                    <i class="fas fa-trash"></i></button>';
                }

                return $button;
            })->rawColumns(['student_full_name', 'payment_status', 'action'])
            ->with([
                'total_credit_amount' => $totalCreditAmount,
            ])
            ->toJson();
    }

    public function getStudentPaymentLogsByPaymentid($payid)
    {
        return  $this->studentPayments::find($payid);
    }

    public function getPaidFees($student_id, $installmentName)
    {
        return $this->studentPayments::where('student_id', $student_id)->where('installment_name', $installmentName)->select('payment_category')->get();
    }

    public function feesDiscount($data, $student_id)
    {


        $installmentIds = $data['installment_id'];
        $discountAmounts =  $data['discount_amount'];

        $feeDetailsId = InstallmentDetails::whereIn('id', $installmentIds)->first()->classroom_fees_id;
        $feeLocked = ClassroomFeesDetails::where('id', $feeDetailsId)->first()->lock;

        if ($feeLocked == 0) {
            return false;
        }

        foreach ($installmentIds as $index => $installmentId) {
            $this->studentFeesDiscount::updateOrCreate(
                [
                    'student_id' => $student_id,
                    'installment_id' => $installmentId,
                ],
                [
                    'student_id' => $student_id,
                    'installment_id' => $installmentId,
                    'discount_amount' => $discountAmounts[$index],
                ]
            );
        }

        return true;
    }

    public function getStudentDiscount($student_id)
    {
        return $this->studentFeesDiscount::where('student_id', $student_id)->get();
    }

    public function getStudentsFeesByClassroom($data)
    {

        $students = $this->admissionRepository->getAllStudentByClassAndDepartment($data);

        $feesData = [];
        if ($data['classroom_id'] != '' && $data['student_id'] == '') {
            foreach ($students as $student) {
                $feesData[$student->id] = $this->getFeesByStudent($student->id);
            }
        } else if ($data['classroom_id'] != '' && $data['student_id'] != '') {
            $feesData[$data['student_id']] = $this->getFeesByStudent($data['student_id']);
        }


        return $feesData;
    }

    public function getFeesByStudent($student_id)
    {
        $feesData = $this->feesView::where('id', $student_id)->get();

        if ($feesData->isNotEmpty()) {
            $groupedData = [];

            foreach ($feesData as $detail) {
                $key = $detail['id'] . '-' . $detail['class_id'];

                if (!isset($groupedData[$key])) {
                    $groupedData[$key] = [
                        'id' => $detail['id'],
                        'first_name' => $detail['first_name'],
                        'middle_name' => $detail['middle_name'],
                        'last_name' => $detail['last_name'],
                        'gr_no' => $detail['gr_no'],
                        'class_id' => $detail['class_id'],
                        'class_name' => $detail['class_name'],
                        'year_name' => $detail['year_name'],
                        'year_id' => $detail['year_id'],
                        'discount_amount' => $detail['discount_amount'] ?? 0,
                        'paid_amount' => $detail['paid_amount'] ?? 0,
                        'total_amount' => 0,
                        'due_amount' => 0,
                        'fee_locked' => $detail['fee_locked'],
                        'installment_data' => [],
                    ];
                }

                // ✅ Push fee category into installment_data array
                $groupedData[$key]['installment_data'][] = [
                    'amount' => $detail['amount'],
                    'fees_category' => $detail['fees_category'],
                    'fees_category_id' => $detail['fees_category_id'],
                ];

                // ✅ Add up amounts
                $groupedData[$key]['total_amount'] = bcadd($groupedData[$key]['total_amount'], $detail['amount'], 2);
                $groupedData[$key]['due_amount'] = bcsub(
                    bcsub($groupedData[$key]['total_amount'], $groupedData[$key]['paid_amount'], 2),
                    $groupedData[$key]['discount_amount'],
                    2
                );
            }

            return array_values($groupedData);
        }

        return [];
    }

    public function getPaidFeesLogs($request, $student_id, $installmentName)
    {
        $perPage = 10;
        $page = $request->query('page', 1);

        return $this->studentPayments::with('getUser')
            ->where('student_id', $student_id)->where('installment_name', $installmentName)
            ->paginate($perPage, ['*'], 'page', $page);
    }
}
