import axiosInstance from '../lib/axios';

export const getTerminatedStudents = async (examId:number,page: number = 1, limit: number = 10): Promise<any> => {
  try {
    const response = await axiosInstance.get(`/quizTermination/${examId}?page=${page}&limit=${limit}`, {
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return {
      success: true,
      data: response.data.data,
      total: response.data.total,
      page: response.data.page,
      limit: response.data.limit,
    };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to get Terminated Students: ${error.response?.data?.message || error.message}`,
    };
  }
};