{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/services/activityLogApi.ts"], "sourcesContent": ["import axiosInstance from '@/lib/axios';\n\nexport interface ActivityLog {\n  id: string;\n  userId: string;\n  userType: 'STUDENT' | 'CLASS';\n  activityType: 'LOGIN' | 'LOGOUT' | 'REGISTRATION';\n  createdAt: string;\n  userDetails?: {\n    id: string;\n    firstName: string;\n    lastName: string;\n    email?: string;\n    contact?: string;\n    contactNo?: string;\n    className?: string;\n    profilePhoto?: string;\n  };\n}\n\nexport interface ActivityLogResponse {\n  logs: ActivityLog[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n  };\n}\n\nexport interface ActivityLogStats {\n  overview: {\n    totalLogs: number;\n    loginCount: number;\n    logoutCount: number;\n    registrationCount: number;\n    studentLogs: number;\n    classLogs: number;\n  };\n  dailyActivity: Array<{\n    activityType: string;\n    _count: number;\n  }>;\n}\n\nexport interface GetActivityLogsParams {\n  page?: number;\n  limit?: number;\n  userType?: string;\n  activityType?: string;\n  search?: string;\n  startDate?: string;\n  endDate?: string;\n}\n\nexport const getActivityLogs = async (params: GetActivityLogsParams = {}): Promise<ActivityLogResponse> => {\n  const response = await axiosInstance.get('/activity-logs', { params });\n  return response.data.data;\n};\n\nexport const getUserActivityLogs = async (\n  userId: string, \n  params: { page?: number; limit?: number; activityType?: string } = {}\n): Promise<ActivityLogResponse> => {\n  const response = await axiosInstance.get(`/activity-logs/user/${userId}`, { params });\n  return response.data.data;\n};\n\nexport const getActivityLogStats = async (params: { startDate?: string; endDate?: string } = {}): Promise<ActivityLogStats> => {\n  const response = await axiosInstance.get('/activity-logs/stats', { params });\n  return response.data.data;\n};\n"], "names": [], "mappings": ";;;;;AAAA;;AAuDO,MAAM,kBAAkB,OAAO,SAAgC,CAAC,CAAC;IACtE,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAa,CAAC,GAAG,CAAC,kBAAkB;QAAE;IAAO;IACpE,OAAO,SAAS,IAAI,CAAC,IAAI;AAC3B;AAEO,MAAM,sBAAsB,OACjC,QACA,SAAmE,CAAC,CAAC;IAErE,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAa,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,QAAQ,EAAE;QAAE;IAAO;IACnF,OAAO,SAAS,IAAI,CAAC,IAAI;AAC3B;AAEO,MAAM,sBAAsB,OAAO,SAAmD,CAAC,CAAC;IAC7F,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAa,CAAC,GAAG,CAAC,wBAAwB;QAAE;IAAO;IAC1E,OAAO,SAAS,IAAI,CAAC,IAAI;AAC3B", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/app/%28dashboard%29/activity-logs/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Search, RefreshCw, User, Clock } from 'lucide-react';\nimport { format } from 'date-fns';\nimport { cn } from '@/lib/utils';\nimport { getActivityLogs, getActivityLogStats, type ActivityLog, type ActivityLogStats } from '@/services/activityLogApi';\nimport { toast } from 'sonner';\n\nexport default function ActivityLogsPage() {\n  const [logs, setLogs] = useState<ActivityLog[]>([]);\n  const [stats, setStats] = useState<ActivityLogStats | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [pagination, setPagination] = useState({\n    page: 1,\n    limit: 20,\n    total: 0,\n    totalPages: 0,\n  });\n\n  const [search, setSearch] = useState('');\n  const [userType, setUserType] = useState<string>('ALL');\n  const [activityType, setActivityType] = useState<string>('ALL');\n\n  const fetchActivityLogs = async (page = 1) => {\n    try {\n      setLoading(true);\n      const params = {\n        page,\n        limit: pagination.limit,\n        ...(search && { search }),\n        ...(userType && userType !== 'ALL' && { userType }),\n        ...(activityType && activityType !== 'ALL' && { activityType }),\n      };\n\n      const response = await getActivityLogs(params);\n      setLogs(response.logs);\n      setPagination(response.pagination);\n    } catch (error) {\n      console.error('Failed to fetch activity logs:', error);\n      toast.error('Failed to fetch activity logs');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchStats = async () => {\n    try {\n      const response = await getActivityLogStats();\n      setStats(response);\n    } catch (error) {\n      console.error('Failed to fetch activity stats:', error);\n      toast.error('Failed to fetch activity statistics');\n    }\n  };\n\n  useEffect(() => {\n    fetchActivityLogs();\n    fetchStats();\n  }, [userType, activityType]);\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between px-5\">\n        <h1 className=\"text-3xl font-bold\">User Activity Logs</h1>\n        <Button onClick={() => fetchActivityLogs(pagination.page)} disabled={loading}>\n          <RefreshCw className={cn('h-4 w-4 mr-2', loading && 'animate-spin')} />\n          Refresh\n        </Button>\n      </div>\n\n\n      \n\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAGA;AAGA;AAEA;AACA;AACA;AAZA;;;;;;;;AAce,SAAS;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;IAC5D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,MAAM;QACN,OAAO;QACP,OAAO;QACP,YAAY;IACd;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEzD,MAAM,oBAAoB,OAAO,OAAO,CAAC;QACvC,IAAI;YACF,WAAW;YACX,MAAM,SAAS;gBACb;gBACA,OAAO,WAAW,KAAK;gBACvB,GAAI,UAAU;oBAAE;gBAAO,CAAC;gBACxB,GAAI,YAAY,aAAa,SAAS;oBAAE;gBAAS,CAAC;gBAClD,GAAI,gBAAgB,iBAAiB,SAAS;oBAAE;gBAAa,CAAC;YAChE;YAEA,MAAM,WAAW,MAAM,CAAA,GAAA,iIAAA,CAAA,kBAAe,AAAD,EAAE;YACvC,QAAQ,SAAS,IAAI;YACrB,cAAc,SAAS,UAAU;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,iIAAA,CAAA,sBAAmB,AAAD;YACzC,SAAS;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;IACF,GAAG;QAAC;QAAU;KAAa;IAE3B,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAqB;;;;;;8BACnC,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAS,IAAM,kBAAkB,WAAW,IAAI;oBAAG,UAAU;;sCACnE,8OAAC,gNAAA,CAAA,YAAS;4BAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB,WAAW;;;;;;wBAAmB;;;;;;;;;;;;;;;;;;AAUjF", "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "file": "refresh-cw.js", "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/node_modules/lucide-react/src/icons/refresh-cw.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8', key: 'v9h5vc' }],\n  ['path', { d: 'M21 3v5h-5', key: '1q7to0' }],\n  ['path', { d: 'M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16', key: '3uifl3' }],\n  ['path', { d: 'M8 16H3v5', key: '1cv678' }],\n];\n\n/**\n * @component @name RefreshCw\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAxMmE5IDkgMCAwIDEgOS05IDkuNzUgOS43NSAwIDAgMSA2Ljc0IDIuNzRMMjEgOCIgLz4KICA8cGF0aCBkPSJNMjEgM3Y1aC01IiAvPgogIDxwYXRoIGQ9Ik0yMSAxMmE5IDkgMCAwIDEtOSA5IDkuNzUgOS43NSAwIDAgMS02Ljc0LTIuNzRMMyAxNiIgLz4KICA8cGF0aCBkPSJNOCAxNkgzdjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/refresh-cw\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst RefreshCw = createLucideIcon('refresh-cw', __iconNode);\n\nexport default RefreshCw;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAsD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACnF;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAuD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACpF;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC5C,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}