@include('auth.header')
<body class="hold-transition login-page">
    <div class="container-fluid">
        <div class="row no-gutter">
            <div class="col-md-6 d-none d-md-flex bg-image"></div>
            <div class="col-md-6 bg-light">
                <div class="login d-flex align-items-center py-5">
                    <div class="container">
                        <div class="row">
                            <div class="col-lg-10 col-xl-7 mx-auto">
                                <div class="content">
                                    <img src="{{ $tenantLogo }}" alt="logo" class="mb-4" style="width:170px;" />
                                    <p class="text-muted mb-4">Please Add Email And New Password to Continue.</p>
                                </div>
                                <form method="POST" action="{{ route('password.update') }}">
                                    @csrf

                                    <input type="hidden" name="token" value="{{ $token }}">

                                    <div class="form-group mb-3">
                                        <input id="email" type="email" placeholder="Email Address" class="form-control shadow-sm px-4 @error('email') is-invalid @enderror" name="email" value="{{ $email ?? old('email') }}" required autocomplete="email" autofocus>

                                        @error('email')
                                        <span class="invalid-feedback" role="alert">
                                            <strong>{{ $message }}</strong>
                                        </span>
                                        @enderror
                                    </div>

                                    <div class="form-group mb-3">
                                        <input id="password" type="password" placeholder="Password" class="form-control shadow-sm px-4 @error('password') is-invalid @enderror" name="password" required autocomplete="new-password">

                                        @error('password')
                                        <span class="invalid-feedback" role="alert">
                                            <strong>{{ $message }}</strong>
                                        </span>
                                        @enderror
                                    </div>

                                    <div class="form-group mb-3">
                                        <input id="password-confirm" placeholder="Confirm Password" type="password" class="form-control" name="password_confirmation" required autocomplete="new-password">
                                    </div>
                                    <button type="submit" class="btn btn-primary btn-block text-uppercase mb-2 shadow-sm">
                                        {{ __('Reset Password') }}
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bottom-copyright">
            <img src="{{ asset("images/rann-dass.png") }}" alt="logo" class="mb-4 rann-dass-logo"
            style="width:200px;" />
            <p class="text-muted mb-4"><strong>Copyright ©
                    <a href="https://www.uest.in/">Uest</a>.
                </strong> All rights reserved. </p>
        </div>
    </div>
</body>
</html>