(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/[turbopack]_browser_dev_hmr-client_hmr-client_ts_6fdd03ea._.js", {

"[turbopack]/browser/dev/hmr-client/hmr-client.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/[turbopack]_browser_dev_hmr-client_hmr-client_ts_61dcf9ba._.js",
  "static/chunks/[turbopack]_browser_dev_hmr-client_hmr-client_ts_23daee15._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[turbopack]/browser/dev/hmr-client/hmr-client.ts [app-client] (ecmascript)");
    });
});
}}),
}]);