<style>
    .log_history {
        table-layout: auto;
        width: 100%;
    }
    .log_history td {
        word-wrap: break-word;
        white-space: normal;
    }
</style>

<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                @php
                    $studentAcademicInfo = getStudentAcademicById($studentid);
                @endphp
                <div class="card card-default">
                    <div class="card-body">
                        {!! Form::open(['id' => 'addroutes_form']) !!}
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Waypoint *</label>
                                    <select id="student_waypoint" class="form-control select2" name="waypoint">
                                        <option value="">Select Waypoint</option>
                                        @foreach ($waypoints as $value)
                                            <option value="{{ $value->id }}"
                                                {{ isset($studentAcademicInfo) && $studentAcademicInfo->waypoint == $value->id ? 'selected' : '' }}>
                                                {{ $value->waypoint_name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Routes *</label>
                                    <select id="student_route" class="form-control select2 route-data" name="route">
                                        <option value="">Select Routes</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div id="routeDetails"></div>
                            </div>
                            <div class="col-md-12">
                                {!! Form::submit('Submit', ['id' => 'saveroutes', 'class' => 'btn btn-primary']) !!}
                                <button data-dismiss="modal" class="btn btn-secondary ml-2">Cancel</button>
                            </div>
                            <div class="col-md-12 mt-5">
                                <div id="routeslogs"
                                    data-initial-content="{{ addslashes($logs->isNotEmpty()? '<h4>Change History</h4><table class=\"table table-bordered\"><thead><tr><th>Change History</th></tr></thead><tbody>' .collect($logs)->map(function ($waypoints) {return '<tr><td><strong>' . e($waypoints->created_by) . '</strong> ' . ($waypoints->oldWaypoint || $waypoints->oldRoute ? 'changed ' . e($waypoints->oldWaypoint ? $waypoints->oldWaypoint->waypoint_name : 'None') . '-waypoint, ' . e($waypoints->oldRoute ? $waypoints->oldRoute->route_name : 'None') . '-route <strong>into</strong> ' : 'set ') . e($waypoints->newWaypoint ? $waypoints->newWaypoint->waypoint_name : 'None') . '-waypoint, ' . e($waypoints->newRoute ? $waypoints->newRoute->route_name : 'None') . '-route <strong>at</strong> <span style=\"color: gray; font-style: italic;\">' . e($waypoints->created_at->timezone('Asia/Kolkata')->format('F d, Y \a\t h:i A')) . '</span></td></tr>';})->implode('') .'</tbody></table>': '<p>No waypoint or route change history available.</p>') }}">
                                    <div class="form-group">
                                        <h4>Activity</h4>
                                        <table class="table table-bordered table-striped log_history"
                                            style="width: 100%;">
                                            <thead>
                                                <tr>
                                                    <th mt-1>Change History</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @forelse($logs as $waypoints)
                                                    <tr>
                                                        <td>
                                                            <strong>{{ $waypoints->created_by }}</strong>
                                                            @if ($waypoints->oldWaypoint || $waypoints->oldRoute)
                                                                changed
                                                                {{ $waypoints->oldWaypoint->waypoint_name ?? 'N/A' }}-waypoint,
                                                                {{ $waypoints->oldRoute->route_name ?? 'N/A' }}-route
                                                                <strong>into</strong>
                                                            @else
                                                                set
                                                            @endif
                                                            {{ $waypoints->newWaypoint->waypoint_name ?? 'N/A' }}-waypoint,
                                                            {{ $waypoints->newRoute->route_name ?? 'N/A' }}-route
                                                            <strong>at</strong>
                                                            <span
                                                                style="color: gray; font-style: italic;">{{ $waypoints->created_at->timezone('Asia/Kolkata')->format('F d, Y \a\t h:i A') }}</span>
                                                        </td>
                                                    </tr>
                                                @empty
                                                    <tr>
                                                        <td colspan="1">No route change history available.</td>
                                                    </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            <input name="vehicle_capacity" id="vehicle_capacity" type="hidden" />
                            <input name="student_allocated" id="student_allocated" type="hidden" />
                        </div>
                        {!! Form::close() !!}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{!! JsValidator::formRequest('Admission\Http\Requests\CreateStudentWaypointRequest', '#addroutes_form') !!}
<script>
    $(document).ready(function() {
        const initialRouteslogs = $('#routeslogs').html();
        console.log('Initial routeslogs content:', initialRouteslogs);

        $('#newWaypointEntry').on('shown.bs.modal', function() {
            console.log('Modal opened, routeslogs content:', $('#routeslogs').html());
        });


        $('#restoreRouteslogs').on('click', function() {
            $('#routeslogs').html($('#routeslogs').data('initial-content'));
            console.log('routeslogs restored:', $('#routeslogs').html());
        });

        $('#student_waypoint').select2();
        $('#student_route').select2();

        function populateRoutes(waypointId) {
            if (!waypointId) return;
            $.ajax({
                url: '/get-routes/' + waypointId,
                type: 'GET',
                success: function(data) {
                    $('#student_route').html(data);
                    $('#student_route').val({{ isset($studentAcademicInfo) && $studentAcademicInfo->route ? $studentAcademicInfo->route : 'null' }}).trigger('change');
                    console.log('Routes populated:', data);
                    console.log('routeslogs after route population:', $('#routeslogs').html());
                },
                error: function(xhr) {
                    console.error('Failed to populate routes:', xhr.responseText);
                }
            });
        }

        $('#student_waypoint').on('change', function() {
            const waypointId = $(this).val();
            populateRoutes(waypointId);
        });

        $('#student_waypoint').trigger('change');

        $("#addroutes_form").submit(function(event) {
            event.preventDefault();

            var form = $(this)[0];
            var formData = new FormData(form);

            let vehicleCapacity = parseInt($("#vehicle_capacity").val());
            let studentCount = parseInt($("#student_allocated").val());

            console.log('Vehicle Capacity:', vehicleCapacity, 'Student Count:', studentCount);
            if (studentCount >= vehicleCapacity) {
                toastr.error("Cannot add more students. Vehicle capacity exceeded!");
                return false;
            }

            if ($(this).valid()) {
                ajaxHandler(form, "{{ route('save.routes-to-student', $studentid) }}", 'POST',
                '#addroutes_form', '#saveroutes', '#newWaypointEntry', '#admissions_table');
                return false;
            }
        });

        $('#student_waypoint').trigger('change');

        setTimeout(() => {
            $('#student_route').val({{ $studentAcademicInfo->route ?? 'null' }}).trigger('change');
        }, 500);
    });
</script>