<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Symfony\Component\Process\Process;
use App\Http\APIControllers\StudentAPIController;
use Admission\Http\Controllers\AdmissionController;


Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

Route::group(['prefix' => 'student'], function () {
    Route::get('get-profiles', [StudentAPIController::class, 'getProfiles']);
    Route::get('get-student-details', [StudentAPIController::class, 'getStudentDetails']);
    // Route::post('logout', [StudentAPIController::class, 'logout']);

    Route::get('get-timetable', [StudentAPIController::class, 'getTimetable']);
    // Route::get('get-feesdetails', [StudentAPIController::class, 'getFeesDetails']);
    // Route::get('get-paymentLogs', [StudentAPIController::class, 'getFeesPaymentLogs']);
    // Route::get('download-receipts/{id}', [StudentAPIController::class, 'downloadSlips']);
    Route::get('get-attendance', [StudentAPIController::class, 'getAttendance']);
    Route::get('get-discipline-issue', [StudentAPIController::class, 'getDisciplineIssue']);
});


Route::post('/run-create-school', function (Request $request) {

    $apiKey = $request->header('X-API-KEY');
    
    if ($apiKey !== env('SCHOOL_CREATION_API_KEY')) {
        return response()->json(['error' => 'Unauthorized'], 403);
    }

    $subdomain = $request->input('subdomain');
    $schoolName = $request->input('school_name');

    if (!$subdomain || !$schoolName) {
        return response()->json(['error' => 'Invalid input'], 400);
    }

    $process = new Process(['/opt/homebrew/bin/php', base_path('artisan'), 'school:create', $subdomain, $schoolName]);
    $process->setTimeout(300);
    $process->start();

    $output = [];

    foreach ($process as $data) {
        $output[] = $data;
    }

    return response()->json([
        'message' => "School creation started!",
        'log' => $output
    ]);
});

Route::get('/students-all-raw', function (Request $request) {
    $apiKey = $request->header('X-API-KEY');
    
    if ($apiKey !== env('SCHOOL_CREATION_API_KEY')) {
        return response()->json(['error' => 'Unauthorized'], 403);
    }

    return app(AdmissionController::class)->getAllStudentsRawAPI($request);
});