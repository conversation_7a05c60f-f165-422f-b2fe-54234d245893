$("#submit-student-details").click(function() {
    studentDetailsSubmit('#student-details-forms', admissionCreateRoute.storeStudentDetails)
});

$("#submit-parents-details").click(function() {
    studentDetailsSubmit('#parents-details-forms', admissionCreateRoute.storeStudentParentsDetails)
});

$("#submit-siblings-details").click(function() {
    studentDetailsSubmit('#siblings-details-forms', admissionCreateRoute.storeStudentSiblingsDetails)
});

$("#submit-health-details").click(function() {
    studentDetailsSubmit('#health-details-forms', admissionCreateRoute.storeStudentHealthDetails)
});

$("#submit-past-details").click(function() {
    studentDetailsSubmit('#past-details-forms', admissionCreateRoute.storeStudentPastDetails)
});


function studentDetailsSubmit(formid, url) {
    event.preventDefault();
    let form = $(formid);
    if (form.valid()) {
        let formData = new FormData();
        let serializedForm = form.serializeArray();
        $.each(serializedForm, function(index, obj){
            formData.append(obj.name, obj.value);
        });
        let fileInput = form.find('input[type="file"]');
        if (fileInput.length > 0) {
            let file = fileInput[0].files[0];
            formData.append('photo', file);
        }

        $.ajax({
            url: url,
            type: "POST",
            data: formData,
            processData: false,
            contentType: false,
            beforeSend: function() {
                $('.page-loader').show();
            },
            success: function (result) {
                if(result.success) {
                    $("html, body").animate({ scrollTop: 0 }, "slow");
                    stepper.next();
                    toastr.success(result.success);
                    if (formid == "#student-details-forms") {
                        $('[name="student_id"]').val(result.id);
                        let href = $('.gotourl').attr('href').replace(':id', result.id);
                        $('.gotourl').attr('href', href);
                        getParentDetails();
                    }
                } else {
                    toastr.error(result.error);
                }
            },
            complete: function() {
                $('.page-loader').hide();
            },
            error: err => {
                if (err.status === 422) {
                    showErrors(err.responseJSON.errors);
                }
                toastr.error('Something went wrong!');
                showLoader();
                return false;
            }
        });
    }
}


$(document).ready(function () {
    let url = window.location.href;
    if (url.indexOf("enq") !== -1) {
        let params = $.extend({}, doAjax_params_default);
        let queryString = url.split('?')[1];
        let enqValue = queryString.split('enq=')[1];
        params["requestType"] = `GET`;
        params["url"] =     params["url"] = window.location.origin + checkHost() + '/enquiry/' + enqValue;;
        params["successCallbackFunction"] = function successCallbackFunction(
            result
        ) {
            $('#first_name').val(result.student_first_name);
            $('#middle_name').val(result.student_middle_name);
            $('#last_name').val(result.student_last_name);
            $('#contact_no_1').val(result.contact_number);
            $('#contact_no_1').val(result.contact_number);
            $('#fathers_name').val(result.student_middle_name);
            $('#fathers_last_name').val(result.student_last_name);
            $('#department').val(result.department).trigger('change');
            setTimeout(function(){
                $('#classroom').val(result.classroom).trigger('change');
            },1000)
            $('#email').val(result.email);
            $('#student-details-forms').append("<input type='hidden' name='enquiry' value="+ enqValue +">");
        };
        commonAjax(params);
    }
});

$('#waypoint').change(function() {
    if($(this).val()) {
        let params = $.extend({}, doAjax_params_default);
        params['url'] = window.location.origin + checkHost() + "/student/routes/" + $(this).val();
        params['requestType'] = `GET`;
        params['successCallbackFunction'] = function successCallbackFunction(response) {
            let select = $('.route-data');
                select.empty();
                select.append($('<option value="">Select Routes</option>'));
                $.each(response.routes, function(index, value) {
                    select.append(
                        $('<option value="' + value.id + '">' + value.route_name + '</option>')
                    );
                });
        }
        commonAjax(params);
    }
});

function getParentDetails()
{
    let params = $.extend({}, doAjax_params_default);
    params["requestType"] = `POST`;
    params["url"] = window.location.origin + checkHost() + '/student-parent-details';
    params['data'] = { 
        'contact_no': $('#contact_no').val(),
        'student_id' : $('[name="student_id"]').val()
     };
    params["successCallbackFunction"] = function successCallbackFunction(
        result
    ) {
        if (result.success) {
            $.each(result.parentdetail, function(fieldName, value) {
                if (fieldName != 'student_id') {
                    $('[name=' + fieldName + ']').val(value);
                    $('[name=' + fieldName + ']').attr("readonly","readonly");
                }
            });
        }
    };
    commonAjax(params);
}


$('#gr_no').change(function() {
    if($(this).val() && $(this).valid()) {
        let params = $.extend({}, doAjax_params_default);
        let studentId = $('[name="student_id"]').val();
        params['url'] = window.location.origin + checkHost() + "/check-grno/?gr_no=" + $(this).val() + '&student_id=' + studentId;
        params['requestType'] = `GET`;
        params['successCallbackFunction'] = function successCallbackFunction(response) {
            $('#gr_no').parent().find('.invalid-feedback').remove();

            if (response.error) {
                $('#gr_no').addClass('is-invalid');
                $('#gr_no').after('<div class="invalid-feedback">' + response.error + '</div>');
            } else {
                $('#gr_no').removeClass('is-invalid');
                $('#gr_no').parent().find('.invalid-feedback').remove();
            }
        }
        commonAjax(params);
    }
});


$('#date_of_birth').change(function() {
    var age = moment().diff(moment($(this).val(), "YYYY-MM-DD"), 'years');
    $("#age").val(age);
});

$("#date_of_birth").datepicker({
        dateFormat: "yy-mm-dd",
        changeMonth: true,
        changeYear: true,
 });