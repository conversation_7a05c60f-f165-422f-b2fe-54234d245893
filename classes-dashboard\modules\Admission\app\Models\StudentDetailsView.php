<?php

namespace Admission\Models;

use Illuminate\Database\Eloquent\Model;
use Routes\Models\Route;
use StudentsModule\Models\Transport\WaypointFees;

class StudentDetailsView extends Model
{
    public $table = 'student_details_view';

    public function getAcademicInfo()
    {
        return $this->belongsTo(StudentAcademicInfo::class, 'id', 'student_id')->withDefault();
    }


    public function getRoutes()
    {
        return $this->belongsTo(Route::class, 'route_id', 'id')->withDefault();
    }
}

