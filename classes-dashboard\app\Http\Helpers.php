<?php

use Admission\Models\LeavingCerificate;
use Admission\Models\StudentAcademicInfo;
use App\Exports\CommonExport;
use Classroom\Models\Classroom;
use Department\Models\Department;
use Enquiry\Models\EnquiryFeeSetup;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Maatwebsite\Excel\Facades\Excel;
use Years\Models\Years;
use Years\Models\YearWithUser;
use Illuminate\Support\Str;

if (!function_exists('export_encoded')) {
    function export_encoded($file)
    {
        return "data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64," . base64_encode($file);
    }
}


if (!function_exists('status_color')) {
    function status_color($status)
    {
        if ($status == config('constants.STATUS.APPROVED')) {
            return '<span class="badge bg-success">' . $status . '</span>';
        } elseif ($status == config('constants.STATUS.REJECT')) {
            return '<span class="badge bg-danger">' . $status . '</span>';
        } elseif ($status == config('constants.STATUS.PENDING')) {
            return '<span class="badge bg-warning">' . $status . '</span>';
        } elseif ($status == config('constants.STATUS.EXPIRED')) {
            return '<span class="badge bg-orange">' . $status . '</span>';
        } else {
            return '<span class="badge bg-primary">' . $status . '</span>';
        }
        return $status;
    }
}

if (!function_exists('date_formatter')) {
    function date_formatter($date)
    {
        return date('d M Y', strtotime($date));
    }
}


if (!function_exists('departmentForStudentPortal')) {
    function departmentForStudentPortal()
    {
        return Department::where('educational', 1)->where('class_uuid', Auth::id())->select('id', 'name')->get();
    }
}

if (!function_exists('payment_color')) {
    function payment_color($status)
    {
        if ($status == config('constants.PAYMENT_STATUS.SUCCESS')) {
            return '<span class="badge bg-success">' . $status . '</span>';
        } elseif ($status == config('constants.PAYMENT_STATUS.FAILED')) {
            return '<span class="badge bg-danger">' . $status . '</span>';
        } else {
            return '<span class="badge bg-primary">' . $status . '</span>';
        }
        return $status;
    }
}

if (!function_exists('getDepartmentFromId')) {
    function getDepartmentFromId($id)
    {
        return Department::where('id', $id)->select('id','name')->first();
    }
}

if (!function_exists('getClassroomFromId')) {
    function getClassroomFromId($id)
    {
        return Classroom::where('id', $id)->select('id','class_name')->first();
    }
}

if (!function_exists('getHeadsDepartment')) {
    function getHeadsDepartment($headid)
    {
        return Department::where('head', $headid)->pluck('id')->toArray();
    }
}

if (!function_exists('isUserHead')) {
    function isUserHead($user_id)
    {
        return Department::where('head', $user_id)->exists();
    }
}

if (!function_exists('checkLCGenerated')) {
    function checkLCGenerated($student_id)
    {
        $lcdata = LeavingCerificate::where('student_id', $student_id)->first();

        if (isset($lcdata)) {
           return true;
        } else {
            return false;
        }

    }
}

if (!function_exists('getActiveYearId')) {
    function getActiveYearId()
    {
        $subdomain = request()->getHost();
        $userId = Auth::id();
        $cacheKey = 'active_year_id_' . $userId . $subdomain;

        return Cache::remember($cacheKey, now()->addMinutes(60), function () use ($userId) {
            $yearWithUser = YearWithUser::where("class_uuid", $userId)->first();

            if ($yearWithUser) {
                $year = Years::where("id", $yearWithUser->year_id)
                    ->where("class_uuid", $userId)
                    ->first();

                if ($year) {
                    return $year->id;
                }
            }

            $year = Years::where('status', 'ACTIVE')
                ->where("class_uuid", $userId)
                ->first();

            if ($year) {
                return $year->id;
            }

            $startDate = now()->startOfMonth();
            $endDate = $startDate->copy()->addYear()->subDay();

            $newYear = Years::create([
                'year_name' => $startDate->format('Y') . '-' . $endDate->format('Y'),
                'start_date' => $startDate,
                'end_date' => $endDate,
                'class_uuid' => $userId,
                'status' => 'ACTIVE',
            ]);

              YearWithUser::create([
                'year_id' => $newYear->id,
                'class_uuid' => $userId,
            ]);

            return $newYear->id;
        });
    }
}

if (!function_exists('geActiveYear')) {
    function geActiveYearName()
    {
        $userId = Auth::id();
        $year = YearWithUser::where("class_uuid", $userId)->first();

        if (isset($year)) {
            $yearData = Years::where("id", $year->year_id)->where("class_uuid", $userId)->first();
        } else {
            $yearData = Years::where('status', 'ACTIVE')->where("class_uuid", $userId)->first();
        }

        return $yearData;
    }
}

if (!function_exists('geAllYear')) {
    function geAllYear()
    {
        $userId = Auth::id();
        $cacheKey = 'all_years' . $userId;
        $subdomain = request()->getHost();

        return Cache::remember($cacheKey . "_". $subdomain, 60, function () use ($userId) {
            return Years::latest()->where("class_uuid", $userId)->get();
        });
    }
}

if (!function_exists('getYearById')) {
    function getYearById($id)
    {
        return Years::where('id', $id)->first();
    }
}

if (!function_exists('orderColumn')) {
    function orderColumn($request, $list, $model)
    {
        if ($request->has('order.0.column')) {
            $orderColumn = $request->input('order.0.column');
            $orderDirection = $request->input('order.0.dir');
            $list->orderBy($request->input("columns.$orderColumn.data"), $orderDirection);
        } else {
            $list->orderBy($model, "DESC");
        }

        return $list;
    }
}

if (!function_exists('searchColumn')) {
    function searchColumn($columns, $list, $concatenatedColumns = [])
    {
        if ($columns) {
            foreach ($columns as $column) {
                if (!empty($column['search']['value'])) {
                    $columnName = $column['name'];
                    $searchValue = $column['search']['value'];

                    if (in_array($columnName, $concatenatedColumns)) {
                        $list = $list->having($columnName, 'like', '%' . $searchValue . '%');
                    } else {
                        $list = $list->where($columnName, 'like', '%' . $searchValue . '%');
                    }
                }
            }
        }
        return $list;
    }
}

if (!function_exists('convertScoreToGrade')) {
    function convertScoreToGrade($score)
    {
        if ($score >= 181 && $score <= 200) {
            return 'A1';
        } elseif ($score >= 161 && $score <= 180) {
            return 'A2';
        } elseif ($score >= 141 && $score <= 160) {
            return 'B1';
        } elseif ($score >= 121 && $score <= 140) {
            return 'B2';
        } elseif ($score >= 101 && $score <= 120) {
            return 'C1';
        } elseif ($score >= 80 && $score <= 100) {
            return 'C2';
        } elseif ($score >= 67 && $score <= 79) {
            return 'D';
        } elseif ($score < 67) {
            return 'E';
        } else {
            return 'N/A';
        }
    }
}


if (!function_exists('apiResonse')) {
    function apiResonse($message, $data)
    {
        return [
            "success" => true,
            "message" => $message,
            "data" => $data,
        ];
    }
}

if (!function_exists('apiErrorResonse')) {
    function apiErrorResonse($message, $data)
    {
        return [
            "success" => false,
            "message" => $message,
            "error" => $data,
        ];
    }
}


if (!function_exists('getStudentAcademicById')) {
    function getStudentAcademicById($studentId)
    {
        return StudentAcademicInfo::where('student_id', $studentId)->first();
    }
}


if (!function_exists('commonExport')) {
    function commonExport($data, $view, $type)
    {
        if (!empty($data)) {
            $response =  array(
                'name' => ucfirst($type),
                'success' => "true",
                'file' => export_encoded(Excel::raw(new CommonExport($data, $view), \Maatwebsite\Excel\Excel::XLSX)),
            );
            return response()->json($response);
        } else {
            return response()->json(['message' => 'No Data Found!!']);
        }
    }
}

if (!function_exists('isClassTeacher')) {
    function isClassTeacher()
    {
        $classrooms = Classroom::where('year_id', getActiveYearId())->where('class_uuid', Auth::id())->get();
        return $classrooms;
    }
}

if (!function_exists('currencyFormat')) {
    function currencyFormat($amount)
    {
        return number_format($amount, 2, '.', ',');
    }
}

if (!function_exists('getEnquiryFees')) {
    function getEnquiryFees()
    {
        return EnquiryFeeSetup::where('class_uuid', Auth::id())->first();
    }
}


if (!function_exists('tenantData')) {
    function tenantData($key = null)
    {
        $data = App::make('TenantData');
        return $key ? ($data[$key] ?? null) : $data;
    }
}

if (!function_exists('generateUUID')) {
    function generateUUID()
    {
        return (string) Str::uuid();
    }
}