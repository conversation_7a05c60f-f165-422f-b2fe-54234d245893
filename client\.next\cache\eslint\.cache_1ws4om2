[{"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\blogs\\layout.tsx": "1", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\blogs\\page.tsx": "2", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\blogs\\[id]\\page.tsx": "3", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\careers\\apply\\[id]\\page.tsx": "4", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\careers\\details\\[id]\\page.tsx": "5", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\careers\\page.tsx": "6", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\blogs\\add\\page.tsx": "7", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\blogs\\page.tsx": "8", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\app-sidebar.tsx": "9", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\nav-main.tsx": "10", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\nav-user.tsx": "11", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\site-header.tsx": "12", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\thoughtSlider.tsx": "13", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\dashboard\\page.tsx": "14", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\layout.tsx": "15", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\certificates\\certificates-form.tsx": "16", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\certificates\\page.tsx": "17", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\components\\sidebar-nav.tsx": "18", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\description\\description-form.tsx": "19", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\description\\page.tsx": "20", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\education\\education-form.tsx": "21", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\education\\page.tsx": "22", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\experience\\experience-form.tsx": "23", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\experience\\page.tsx": "24", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\layout.tsx": "25", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\page.tsx": "26", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\photo-and-logo\\page.tsx": "27", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\photo-and-logo\\photo-and-logo.tsx": "28", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\profile-form.tsx": "29", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\tution-class\\page.tsx": "30", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\tution-class\\setup-tution-class.tsx": "31", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes-details\\[id]\\page.tsx": "32", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\coins\\page.tsx": "33", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx": "34", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\page.tsx": "35", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\privacy-policy\\layout.tsx": "36", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\privacy-policy\\page.tsx": "37", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\wishlist\\page.tsx": "38", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\support\\FAQSection.tsx": "39", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\support\\page.tsx": "40", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\support\\SupportOptions.tsx": "41", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\terms-and-conditions\\page.tsx": "42", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz\\countDownTimer.tsx": "43", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz\\examStatusButton.tsx": "44", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz\\page.tsx": "45", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz-details\\[examId]\\page.tsx": "46", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verified-classes\\FilterInput.tsx": "47", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verified-classes\\page.tsx": "48", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verify-email\\page.tsx": "49", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\AppDatePicker.tsx": "50", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\AuthActions.tsx": "51", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\AuthErrorHandler.tsx": "52", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\BlogCard.tsx": "53", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\DynamicTable.tsx": "54", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\Footer.tsx": "55", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\Header.tsx": "56", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\MonthYearPicker.tsx": "57", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\RecentBlogs.tsx": "58", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\ReviewsSection.tsx": "59", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\SignUpSignIn.tsx": "60", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\TestimonialSlider.tsx": "61", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\accordion.tsx": "62", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\alert-dialog.tsx": "63", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\avatar.tsx": "64", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\badge.tsx": "65", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\button.tsx": "66", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\calendar.tsx": "67", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\card.tsx": "68", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\checkbox.tsx": "69", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\collapsible.tsx": "70", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\command.tsx": "71", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\CustomModal.tsx": "72", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\dialog.tsx": "73", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\dropdown-menu.tsx": "74", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\form.tsx": "75", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\input.tsx": "76", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\label.tsx": "77", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\multi-select.tsx": "78", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\pagination.tsx": "79", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\popover.tsx": "80", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\progress.tsx": "81", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\select.tsx": "82", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\separator.tsx": "83", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sheet.tsx": "84", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sidebar.tsx": "85", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\skeleton.tsx": "86", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sonner.tsx": "87", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\table.tsx": "88", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\tabs.tsx": "89", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\textarea.tsx": "90", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\tooltip.tsx": "91", "G:\\UEST\\uest_app\\uest-app\\client\\src\\hooks\\use-mobile.ts": "92", "G:\\UEST\\uest_app\\uest-app\\client\\src\\hooks\\useFullScreen.ts": "93", "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\axios.ts": "94", "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\constant\\quizConstant.ts": "95", "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\helper.ts": "96", "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\types.ts": "97", "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\useAuth.ts": "98", "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\utils.ts": "99", "G:\\UEST\\uest_app\\uest-app\\client\\src\\Providers\\theme-provider.tsx": "100", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\AuthService.ts": "101", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\blogApi.ts": "102", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\careerService.ts": "103", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\classesThoughtApi.ts": "104", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\examApi.ts": "105", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\examApplicationApi.ts": "106", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\quizAttemptApi.ts": "107", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\quizTerminationLog.ts": "108", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\reviewsApi.ts": "109", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\studentAuthServices.ts": "110", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\studentWishlistServices.ts": "111", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizCertificateApi.ts": "112", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizExamApi.ts": "113", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizRankingApi.ts": "114", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\index.ts": "115", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\provider.tsx": "116", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\slices\\classSlice.ts": "117", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\slices\\formProgressSlice.ts": "118", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\slices\\userSlice.ts": "119", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\thunks\\classThunks.ts": "120", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\blogs\\add\\AddBlogPageContent.tsx": "121", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\class\\login\\page.tsx": "122", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\question-bank\\page.tsx": "123", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\login\\page.tsx": "124", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\StatsSection.tsx": "125", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\alert.tsx": "126", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\questionBankApi.ts": "127", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz-info\\[examid]\\page.tsx": "128", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\GoogleLoginButton.tsx": "129", "G:\\UEST\\uest_app\\uest-app\\client\\src\\hooks\\AnalyticsProvider.tsx": "130", "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\gtag.ts": "131", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\profile\\page.tsx": "132", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\ProfileCompletionIndicator.tsx": "133", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\hooks.ts": "134", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\slices\\studentProfileSlice.ts": "135", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\thunks\\studentProfileThunks.ts": "136", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\referral-dashboard\\page.tsx": "137", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\referral-dashboard\\page.tsx": "138", "G:\\UEST\\uest_app\\uest-app\\client\\src\\utils\\pdfGenerator.ts": "139", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx": "140", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\referralApi.ts": "141", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\examApplicantEmailApi.ts": "142", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\mockExamButton.tsx": "143", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\page.tsx": "144", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\restictExamAttempt.tsx": "145", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz-exam\\[examId]\\page.tsx": "146", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\quizTeminationApi.ts": "147", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\studentDetailServiceApi.ts": "148", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizMockExamApi.ts": "149", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizPreventReattemptApi.ts": "150", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizQuestionForStudentApi.ts": "151", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizSaveExamAnswerApi.ts": "152", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\chat\\page.tsx": "153", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\chat\\page.tsx": "154", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\SharedChat.tsx": "155", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\chatService.ts": "156", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\address\\AddressForm.tsx": "157", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\address\\page.tsx": "158", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ExamCameraMonitoring.tsx": "159", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\classViewLogService.ts": "160", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\examMonitoringApi.ts": "161", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\PosterDialog.tsx": "162", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\notifications\\page.tsx": "163", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\NotificationBell.tsx": "164", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\notificationService.ts": "165", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\Leader-Board\\page.tsx": "166", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-exam-card\\page.tsx": "167", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-exam-result\\[studentId]\\page.tsx": "168", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\mockExam.tsx": "169", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student-verify-otp\\page.tsx": "170", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verify-otp\\page.tsx": "171", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\badgedisplay.tsx": "172", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\streakcountdisplay.tsx": "173", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\totaluestcoin.tsx": "174", "G:\\UEST\\uest_app\\uest-app\\client\\src\\hooks\\getStudentId.tsx": "175", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\LeaderboardUserApi.ts": "176", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\mock-exam-resultApi.ts": "177", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\mockExamStreakApi.ts": "178", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uestCoinTransctionApi.ts": "179", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizMockExamTerminationApi.ts": "180", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\profile\\components\\sidebar-nav.tsx": "181"}, {"size": 156, "mtime": 1751275811335, "results": "182", "hashOfConfig": "183"}, {"size": 4799, "mtime": 1751275807358, "results": "184", "hashOfConfig": "183"}, {"size": 5240, "mtime": 1751275802728, "results": "185", "hashOfConfig": "183"}, {"size": 29458, "mtime": 1751625232828, "results": "186", "hashOfConfig": "183"}, {"size": 3171, "mtime": 1747109292602, "results": "187", "hashOfConfig": "183"}, {"size": 4002, "mtime": 1747109292604, "results": "188", "hashOfConfig": "183"}, {"size": 226, "mtime": 1751024271963, "results": "189", "hashOfConfig": "183"}, {"size": 5928, "mtime": 1751024275820, "results": "190", "hashOfConfig": "183"}, {"size": 2482, "mtime": 1752596826384, "results": "191", "hashOfConfig": "183"}, {"size": 5384, "mtime": 1747289688954, "results": "192", "hashOfConfig": "183"}, {"size": 3658, "mtime": 1747289688955, "results": "193", "hashOfConfig": "183"}, {"size": 674, "mtime": 1747289688955, "results": "194", "hashOfConfig": "183"}, {"size": 5932, "mtime": 1752465858736, "results": "195", "hashOfConfig": "183"}, {"size": 114, "mtime": 1752596842332, "results": "196", "hashOfConfig": "183"}, {"size": 597, "mtime": 1752596848563, "results": "197", "hashOfConfig": "183"}, {"size": 13660, "mtime": 1749201002331, "results": "198", "hashOfConfig": "183"}, {"size": 550, "mtime": 1747289688983, "results": "199", "hashOfConfig": "183"}, {"size": 1623, "mtime": 1747289688984, "results": "200", "hashOfConfig": "183"}, {"size": 3787, "mtime": 1747990267735, "results": "201", "hashOfConfig": "183"}, {"size": 516, "mtime": 1747289689001, "results": "202", "hashOfConfig": "183"}, {"size": 17426, "mtime": 1752465858750, "results": "203", "hashOfConfig": "183"}, {"size": 551, "mtime": 1747289689003, "results": "204", "hashOfConfig": "183"}, {"size": 14780, "mtime": 1747990267862, "results": "205", "hashOfConfig": "183"}, {"size": 557, "mtime": 1747289689005, "results": "206", "hashOfConfig": "183"}, {"size": 4900, "mtime": 1751625232913, "results": "207", "hashOfConfig": "183"}, {"size": 451, "mtime": 1747289689007, "results": "208", "hashOfConfig": "183"}, {"size": 514, "mtime": 1747289689008, "results": "209", "hashOfConfig": "183"}, {"size": 9870, "mtime": 1748962020705, "results": "210", "hashOfConfig": "183"}, {"size": 9250, "mtime": 1749206244199, "results": "211", "hashOfConfig": "183"}, {"size": 578, "mtime": 1747289689027, "results": "212", "hashOfConfig": "183"}, {"size": 20216, "mtime": 1752486686589, "results": "213", "hashOfConfig": "183"}, {"size": 17836, "mtime": 1752465858733, "results": "214", "hashOfConfig": "183"}, {"size": 13775, "mtime": 1749201002401, "results": "215", "hashOfConfig": "183"}, {"size": 1778, "mtime": 1752570168084, "results": "216", "hashOfConfig": "183"}, {"size": 30747, "mtime": 1752465859132, "results": "217", "hashOfConfig": "183"}, {"size": 531, "mtime": 1747109267499, "results": "218", "hashOfConfig": "183"}, {"size": 21826, "mtime": 1752728150881, "results": "219", "hashOfConfig": "183"}, {"size": 10121, "mtime": 1747624459676, "results": "220", "hashOfConfig": "183"}, {"size": 6100, "mtime": 1747109267569, "results": "221", "hashOfConfig": "183"}, {"size": 354, "mtime": 1747109267605, "results": "222", "hashOfConfig": "183"}, {"size": 4841, "mtime": 1747109267602, "results": "223", "hashOfConfig": "183"}, {"size": 14852, "mtime": 1747109267661, "results": "224", "hashOfConfig": "183"}, {"size": 4990, "mtime": 1749722967811, "results": "225", "hashOfConfig": "183"}, {"size": 4724, "mtime": 1749722967814, "results": "226", "hashOfConfig": "183"}, {"size": 35295, "mtime": 1752465859472, "results": "227", "hashOfConfig": "183"}, {"size": 15866, "mtime": 1749486774681, "results": "228", "hashOfConfig": "183"}, {"size": 1070, "mtime": 1752489573454, "results": "229", "hashOfConfig": "183"}, {"size": 27384, "mtime": 1752489573456, "results": "230", "hashOfConfig": "183"}, {"size": 2942, "mtime": 1747289689132, "results": "231", "hashOfConfig": "183"}, {"size": 2594, "mtime": 1747109292536, "results": "232", "hashOfConfig": "183"}, {"size": 2908, "mtime": 1747624459651, "results": "233", "hashOfConfig": "183"}, {"size": 695, "mtime": 1749485471880, "results": "234", "hashOfConfig": "183"}, {"size": 4253, "mtime": 1747289688716, "results": "235", "hashOfConfig": "183"}, {"size": 9141, "mtime": 1747289688734, "results": "236", "hashOfConfig": "183"}, {"size": 5534, "mtime": 1751625232647, "results": "237", "hashOfConfig": "183"}, {"size": 35740, "mtime": 1752684636345, "results": "238", "hashOfConfig": "183"}, {"size": 1582, "mtime": 1747109267153, "results": "239", "hashOfConfig": "183"}, {"size": 3175, "mtime": 1747289688746, "results": "240", "hashOfConfig": "183"}, {"size": 18291, "mtime": 1747624459652, "results": "241", "hashOfConfig": "183"}, {"size": 9254, "mtime": 1752465858567, "results": "242", "hashOfConfig": "183"}, {"size": 6818, "mtime": 1747654911853, "results": "243", "hashOfConfig": "183"}, {"size": 2125, "mtime": 1747109268031, "results": "244", "hashOfConfig": "183"}, {"size": 4021, "mtime": 1747289689134, "results": "245", "hashOfConfig": "183"}, {"size": 1090, "mtime": 1747109268032, "results": "246", "hashOfConfig": "183"}, {"size": 1634, "mtime": 1747109268033, "results": "247", "hashOfConfig": "183"}, {"size": 2158, "mtime": 1747109268035, "results": "248", "hashOfConfig": "183"}, {"size": 6645, "mtime": 1748363529790, "results": "249", "hashOfConfig": "183"}, {"size": 2003, "mtime": 1747109268037, "results": "250", "hashOfConfig": "183"}, {"size": 1258, "mtime": 1747109268043, "results": "251", "hashOfConfig": "183"}, {"size": 833, "mtime": 1747289689135, "results": "252", "hashOfConfig": "183"}, {"size": 0, "mtime": 1744777321785, "results": "253", "hashOfConfig": "183"}, {"size": 1166, "mtime": 1747624459679, "results": "254", "hashOfConfig": "183"}, {"size": 3914, "mtime": 1747109268044, "results": "255", "hashOfConfig": "183"}, {"size": 8541, "mtime": 1747289689136, "results": "256", "hashOfConfig": "183"}, {"size": 3871, "mtime": 1747109268047, "results": "257", "hashOfConfig": "183"}, {"size": 992, "mtime": 1747109268048, "results": "258", "hashOfConfig": "183"}, {"size": 634, "mtime": 1747109268051, "results": "259", "hashOfConfig": "183"}, {"size": 3738, "mtime": 1752490278767, "results": "260", "hashOfConfig": "183"}, {"size": 2860, "mtime": 1747289689149, "results": "261", "hashOfConfig": "183"}, {"size": 1680, "mtime": 1747109268054, "results": "262", "hashOfConfig": "183"}, {"size": 750, "mtime": 1747109268056, "results": "263", "hashOfConfig": "183"}, {"size": 6382, "mtime": 1747109268057, "results": "264", "hashOfConfig": "183"}, {"size": 738, "mtime": 1747109268059, "results": "265", "hashOfConfig": "183"}, {"size": 4229, "mtime": 1747289689150, "results": "266", "hashOfConfig": "183"}, {"size": 22359, "mtime": 1747289689157, "results": "267", "hashOfConfig": "183"}, {"size": 292, "mtime": 1747109268060, "results": "268", "hashOfConfig": "183"}, {"size": 596, "mtime": 1747109268061, "results": "269", "hashOfConfig": "183"}, {"size": 2564, "mtime": 1747289689158, "results": "270", "hashOfConfig": "183"}, {"size": 2016, "mtime": 1747109268062, "results": "271", "hashOfConfig": "183"}, {"size": 781, "mtime": 1747109268063, "results": "272", "hashOfConfig": "183"}, {"size": 1952, "mtime": 1747289689159, "results": "273", "hashOfConfig": "183"}, {"size": 584, "mtime": 1749468144347, "results": "274", "hashOfConfig": "183"}, {"size": 7273, "mtime": 1747109268066, "results": "275", "hashOfConfig": "183"}, {"size": 1380, "mtime": 1752561698912, "results": "276", "hashOfConfig": "183"}, {"size": 188, "mtime": 1747109268072, "results": "277", "hashOfConfig": "183"}, {"size": 2293, "mtime": 1752465860247, "results": "278", "hashOfConfig": "183"}, {"size": 8637, "mtime": 1752684636347, "results": "279", "hashOfConfig": "183"}, {"size": 465, "mtime": 1747109268078, "results": "280", "hashOfConfig": "183"}, {"size": 824, "mtime": 1747289689165, "results": "281", "hashOfConfig": "183"}, {"size": 310, "mtime": 1747109267096, "results": "282", "hashOfConfig": "183"}, {"size": 2032, "mtime": 1752465860837, "results": "283", "hashOfConfig": "183"}, {"size": 3382, "mtime": 1751255662795, "results": "284", "hashOfConfig": "183"}, {"size": 843, "mtime": 1747109292640, "results": "285", "hashOfConfig": "183"}, {"size": 1282, "mtime": 1751625233134, "results": "286", "hashOfConfig": "183"}, {"size": 2363, "mtime": 1748260878717, "results": "287", "hashOfConfig": "183"}, {"size": 1193, "mtime": 1748964660194, "results": "288", "hashOfConfig": "183"}, {"size": 438, "mtime": 1747109268089, "results": "289", "hashOfConfig": "183"}, {"size": 508, "mtime": 1747109268089, "results": "290", "hashOfConfig": "183"}, {"size": 1564, "mtime": 1747624459698, "results": "291", "hashOfConfig": "183"}, {"size": 2225, "mtime": 1752465861899, "results": "292", "hashOfConfig": "183"}, {"size": 3017, "mtime": 1747289689201, "results": "293", "hashOfConfig": "183"}, {"size": 1144, "mtime": 1747109268090, "results": "294", "hashOfConfig": "183"}, {"size": 414, "mtime": 1747109268091, "results": "295", "hashOfConfig": "183"}, {"size": 484, "mtime": 1747109268092, "results": "296", "hashOfConfig": "183"}, {"size": 590, "mtime": 1747883078088, "results": "297", "hashOfConfig": "183"}, {"size": 232, "mtime": 1747109268096, "results": "298", "hashOfConfig": "183"}, {"size": 1095, "mtime": 1747109268097, "results": "299", "hashOfConfig": "183"}, {"size": 1516, "mtime": 1750649654430, "results": "300", "hashOfConfig": "183"}, {"size": 1157, "mtime": 1752644594433, "results": "301", "hashOfConfig": "183"}, {"size": 458, "mtime": 1749201002646, "results": "302", "hashOfConfig": "183"}, {"size": 10094, "mtime": 1751024283211, "results": "303", "hashOfConfig": "183"}, {"size": 26618, "mtime": 1752465858731, "results": "304", "hashOfConfig": "183"}, {"size": 28459, "mtime": 1751625232977, "results": "305", "hashOfConfig": "183"}, {"size": 27024, "mtime": 1752465859199, "results": "306", "hashOfConfig": "183"}, {"size": 3193, "mtime": 1748962020700, "results": "307", "hashOfConfig": "183"}, {"size": 1643, "mtime": 1747624459680, "results": "308", "hashOfConfig": "183"}, {"size": 3539, "mtime": 1748260878719, "results": "309", "hashOfConfig": "183"}, {"size": 23835, "mtime": 1752465859456, "results": "310", "hashOfConfig": "183"}, {"size": 5982, "mtime": 1750070278915, "results": "311", "hashOfConfig": "183"}, {"size": 433, "mtime": 1749485552367, "results": "312", "hashOfConfig": "183"}, {"size": 466, "mtime": 1747797160907, "results": "313", "hashOfConfig": "183"}, {"size": 71986, "mtime": 1753155798153, "results": "314", "hashOfConfig": "183"}, {"size": 1825, "mtime": 1750070278881, "results": "315", "hashOfConfig": "183"}, {"size": 356, "mtime": 1747883078087, "results": "316", "hashOfConfig": "183"}, {"size": 4431, "mtime": 1753018117970, "results": "317", "hashOfConfig": "183"}, {"size": 2286, "mtime": 1752919194885, "results": "318", "hashOfConfig": "183"}, {"size": 16992, "mtime": 1751625232990, "results": "319", "hashOfConfig": "183"}, {"size": 17122, "mtime": 1748768935829, "results": "320", "hashOfConfig": "183"}, {"size": 8676, "mtime": 1749015983232, "results": "321", "hashOfConfig": "183"}, {"size": 977, "mtime": 1748768935828, "results": "322", "hashOfConfig": "183"}, {"size": 706, "mtime": 1748768935833, "results": "323", "hashOfConfig": "183"}, {"size": 523, "mtime": 1749201002641, "results": "324", "hashOfConfig": "183"}, {"size": 4714, "mtime": 1752465858945, "results": "325", "hashOfConfig": "183"}, {"size": 218, "mtime": 1752465859129, "results": "326", "hashOfConfig": "183"}, {"size": 1279, "mtime": 1749486774674, "results": "327", "hashOfConfig": "183"}, {"size": 40923, "mtime": 1752465859447, "results": "328", "hashOfConfig": "183"}, {"size": 962, "mtime": 1750070278918, "results": "329", "hashOfConfig": "183"}, {"size": 404, "mtime": 1749486774735, "results": "330", "hashOfConfig": "183"}, {"size": 499, "mtime": 1752465862527, "results": "331", "hashOfConfig": "183"}, {"size": 535, "mtime": 1749885108597, "results": "332", "hashOfConfig": "183"}, {"size": 2278, "mtime": 1749486774737, "results": "333", "hashOfConfig": "183"}, {"size": 460, "mtime": 1749486774737, "results": "334", "hashOfConfig": "183"}, {"size": 905, "mtime": 1752465858734, "results": "335", "hashOfConfig": "183"}, {"size": 2443, "mtime": 1751276652402, "results": "336", "hashOfConfig": "183"}, {"size": 58185, "mtime": 1752595277375, "results": "337", "hashOfConfig": "183"}, {"size": 1171, "mtime": 1751276652417, "results": "338", "hashOfConfig": "183"}, {"size": 8203, "mtime": 1752651150682, "results": "339", "hashOfConfig": "183"}, {"size": 535, "mtime": 1750649654200, "results": "340", "hashOfConfig": "183"}, {"size": 5275, "mtime": 1750649654371, "results": "341", "hashOfConfig": "183"}, {"size": 964, "mtime": 1751276652418, "results": "342", "hashOfConfig": "183"}, {"size": 841, "mtime": 1750649654428, "results": "343", "hashOfConfig": "183"}, {"size": 1293, "mtime": 1751625233132, "results": "344", "hashOfConfig": "183"}, {"size": 21787, "mtime": 1752595277376, "results": "345", "hashOfConfig": "183"}, {"size": 11414, "mtime": 1752595277373, "results": "346", "hashOfConfig": "183"}, {"size": 3319, "mtime": 1751625233498, "results": "347", "hashOfConfig": "183"}, {"size": 14732, "mtime": 1752465858569, "results": "348", "hashOfConfig": "183"}, {"size": 18518, "mtime": 1752465858924, "results": "349", "hashOfConfig": "183"}, {"size": 11665, "mtime": 1752465858933, "results": "350", "hashOfConfig": "183"}, {"size": 43223, "mtime": 1752465858936, "results": "351", "hashOfConfig": "183"}, {"size": 13760, "mtime": 1752465859165, "results": "352", "hashOfConfig": "183"}, {"size": 14090, "mtime": 1752465859848, "results": "353", "hashOfConfig": "183"}, {"size": 1336, "mtime": 1752465859850, "results": "354", "hashOfConfig": "183"}, {"size": 1051, "mtime": 1752465859906, "results": "355", "hashOfConfig": "183"}, {"size": 1196, "mtime": 1752465859911, "results": "356", "hashOfConfig": "183"}, {"size": 1101, "mtime": 1752465860219, "results": "357", "hashOfConfig": "183"}, {"size": 680, "mtime": 1752465860839, "results": "358", "hashOfConfig": "183"}, {"size": 1114, "mtime": 1752465861652, "results": "359", "hashOfConfig": "183"}, {"size": 1056, "mtime": 1752465861807, "results": "360", "hashOfConfig": "183"}, {"size": 951, "mtime": 1752465862526, "results": "361", "hashOfConfig": "183"}, {"size": 941, "mtime": 1752465862528, "results": "362", "hashOfConfig": "183"}, {"size": 2152, "mtime": 1753089471657, "results": "363", "hashOfConfig": "183"}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1uj1650", {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "712", "messages": "713", "suppressedMessages": "714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "715", "messages": "716", "suppressedMessages": "717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "718", "messages": "719", "suppressedMessages": "720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "721", "messages": "722", "suppressedMessages": "723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "724", "messages": "725", "suppressedMessages": "726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "727", "messages": "728", "suppressedMessages": "729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "730", "messages": "731", "suppressedMessages": "732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "733", "messages": "734", "suppressedMessages": "735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "736", "messages": "737", "suppressedMessages": "738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "739", "messages": "740", "suppressedMessages": "741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "742", "messages": "743", "suppressedMessages": "744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "745", "messages": "746", "suppressedMessages": "747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "748", "messages": "749", "suppressedMessages": "750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "751", "messages": "752", "suppressedMessages": "753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "754", "messages": "755", "suppressedMessages": "756", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "757", "messages": "758", "suppressedMessages": "759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "760", "messages": "761", "suppressedMessages": "762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "763", "messages": "764", "suppressedMessages": "765", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "766", "messages": "767", "suppressedMessages": "768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "769", "messages": "770", "suppressedMessages": "771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "772", "messages": "773", "suppressedMessages": "774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "775", "messages": "776", "suppressedMessages": "777", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "778", "messages": "779", "suppressedMessages": "780", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "781", "messages": "782", "suppressedMessages": "783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "784", "messages": "785", "suppressedMessages": "786", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "787", "messages": "788", "suppressedMessages": "789", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "790", "messages": "791", "suppressedMessages": "792", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "793", "messages": "794", "suppressedMessages": "795", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "796", "messages": "797", "suppressedMessages": "798", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "799", "messages": "800", "suppressedMessages": "801", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "802", "messages": "803", "suppressedMessages": "804", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "805", "messages": "806", "suppressedMessages": "807", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "808", "messages": "809", "suppressedMessages": "810", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "811", "messages": "812", "suppressedMessages": "813", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "814", "messages": "815", "suppressedMessages": "816", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "817", "messages": "818", "suppressedMessages": "819", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "820", "messages": "821", "suppressedMessages": "822", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "823", "messages": "824", "suppressedMessages": "825", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "826", "messages": "827", "suppressedMessages": "828", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "829", "messages": "830", "suppressedMessages": "831", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "832", "messages": "833", "suppressedMessages": "834", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "835", "messages": "836", "suppressedMessages": "837", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "838", "messages": "839", "suppressedMessages": "840", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "841", "messages": "842", "suppressedMessages": "843", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "844", "messages": "845", "suppressedMessages": "846", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "847", "messages": "848", "suppressedMessages": "849", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "850", "messages": "851", "suppressedMessages": "852", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "853", "messages": "854", "suppressedMessages": "855", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "856", "messages": "857", "suppressedMessages": "858", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "859", "messages": "860", "suppressedMessages": "861", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "862", "messages": "863", "suppressedMessages": "864", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "865", "messages": "866", "suppressedMessages": "867", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "868", "messages": "869", "suppressedMessages": "870", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "871", "messages": "872", "suppressedMessages": "873", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "874", "messages": "875", "suppressedMessages": "876", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "877", "messages": "878", "suppressedMessages": "879", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "880", "messages": "881", "suppressedMessages": "882", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "883", "messages": "884", "suppressedMessages": "885", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "886", "messages": "887", "suppressedMessages": "888", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "889", "messages": "890", "suppressedMessages": "891", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "892", "messages": "893", "suppressedMessages": "894", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "895", "messages": "896", "suppressedMessages": "897", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "898", "messages": "899", "suppressedMessages": "900", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "901", "messages": "902", "suppressedMessages": "903", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "904", "messages": "905", "suppressedMessages": "906", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\blogs\\layout.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\blogs\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\blogs\\[id]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\careers\\apply\\[id]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\careers\\details\\[id]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\careers\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\blogs\\add\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\blogs\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\app-sidebar.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\nav-main.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\nav-user.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\site-header.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\thoughtSlider.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\dashboard\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\layout.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\certificates\\certificates-form.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\certificates\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\components\\sidebar-nav.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\description\\description-form.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\description\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\education\\education-form.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\education\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\experience\\experience-form.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\experience\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\layout.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\photo-and-logo\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\photo-and-logo\\photo-and-logo.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\profile-form.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\tution-class\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\tution-class\\setup-tution-class.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes-details\\[id]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\coins\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\page.tsx", ["907"], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\privacy-policy\\layout.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\privacy-policy\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\wishlist\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\support\\FAQSection.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\support\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\support\\SupportOptions.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\terms-and-conditions\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz\\countDownTimer.tsx", ["908"], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz\\examStatusButton.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz-details\\[examId]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verified-classes\\FilterInput.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verified-classes\\page.tsx", [], ["909"], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verify-email\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\AppDatePicker.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\AuthActions.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\AuthErrorHandler.tsx", [], ["910"], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\BlogCard.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\DynamicTable.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\Footer.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\Header.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\MonthYearPicker.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\RecentBlogs.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\ReviewsSection.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\SignUpSignIn.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\TestimonialSlider.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\accordion.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\alert-dialog.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\avatar.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\badge.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\button.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\calendar.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\card.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\checkbox.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\collapsible.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\command.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\CustomModal.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\dialog.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\dropdown-menu.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\form.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\input.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\label.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\multi-select.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\pagination.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\popover.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\progress.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\select.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\separator.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sheet.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sidebar.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\skeleton.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sonner.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\table.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\tabs.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\textarea.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\tooltip.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\hooks\\use-mobile.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\hooks\\useFullScreen.ts", [], ["911"], "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\axios.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\constant\\quizConstant.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\helper.ts", [], ["912"], "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\types.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\useAuth.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\utils.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\Providers\\theme-provider.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\AuthService.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\blogApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\careerService.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\classesThoughtApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\examApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\examApplicationApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\quizAttemptApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\quizTerminationLog.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\reviewsApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\studentAuthServices.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\studentWishlistServices.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizCertificateApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizExamApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizRankingApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\index.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\provider.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\slices\\classSlice.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\slices\\formProgressSlice.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\slices\\userSlice.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\thunks\\classThunks.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\blogs\\add\\AddBlogPageContent.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\class\\login\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\question-bank\\page.tsx", [], ["913", "914"], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\login\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\StatsSection.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\alert.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\questionBankApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz-info\\[examid]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\GoogleLoginButton.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\hooks\\AnalyticsProvider.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\gtag.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\profile\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\ProfileCompletionIndicator.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\hooks.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\slices\\studentProfileSlice.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\thunks\\studentProfileThunks.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\referral-dashboard\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\referral-dashboard\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\utils\\pdfGenerator.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\referralApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\examApplicantEmailApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\mockExamButton.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\restictExamAttempt.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz-exam\\[examId]\\page.tsx", ["915", "916", "917", "918", "919", "920", "921"], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\quizTeminationApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\studentDetailServiceApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizMockExamApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizPreventReattemptApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizQuestionForStudentApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizSaveExamAnswerApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\chat\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\chat\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\SharedChat.tsx", ["922"], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\chatService.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\address\\AddressForm.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\address\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ExamCameraMonitoring.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\classViewLogService.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\examMonitoringApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\PosterDialog.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\notifications\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\NotificationBell.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\notificationService.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\Leader-Board\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-exam-card\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-exam-result\\[studentId]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\mockExam.tsx", ["923", "924", "925", "926", "927"], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student-verify-otp\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verify-otp\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\badgedisplay.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\streakcountdisplay.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\totaluestcoin.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\hooks\\getStudentId.tsx", ["928"], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\LeaderboardUserApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\mock-exam-resultApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\mockExamStreakApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uestCoinTransctionApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizMockExamTerminationApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\profile\\components\\sidebar-nav.tsx", [], [], {"ruleId": "929", "severity": 1, "message": "930", "line": 233, "column": 6, "nodeType": "931", "endLine": 233, "endColumn": 8, "suggestions": "932"}, {"ruleId": "929", "severity": 1, "message": "933", "line": 100, "column": 6, "nodeType": "931", "endLine": 100, "endColumn": 62, "suggestions": "934"}, {"ruleId": "929", "severity": 1, "message": "935", "line": 258, "column": 6, "nodeType": "931", "endLine": 258, "endColumn": 47, "suggestions": "936", "suppressions": "937"}, {"ruleId": "929", "severity": 1, "message": "938", "line": 21, "column": 6, "nodeType": "931", "endLine": 21, "endColumn": 17, "suggestions": "939", "suppressions": "940"}, {"ruleId": "929", "severity": 1, "message": "941", "line": 193, "column": 6, "nodeType": "931", "endLine": 202, "endColumn": 4, "suggestions": "942", "suppressions": "943"}, {"ruleId": "944", "severity": 2, "message": "945", "line": 44, "column": 15, "nodeType": "946", "messageId": "947", "endLine": 44, "endColumn": 22, "suppressions": "948"}, {"ruleId": "929", "severity": 1, "message": "949", "line": 206, "column": 6, "nodeType": "931", "endLine": 206, "endColumn": 8, "suggestions": "950", "suppressions": "951"}, {"ruleId": "929", "severity": 1, "message": "952", "line": 211, "column": 6, "nodeType": "931", "endLine": 211, "endColumn": 42, "suggestions": "953", "suppressions": "954"}, {"ruleId": "929", "severity": 1, "message": "955", "line": 111, "column": 6, "nodeType": "931", "endLine": 111, "endColumn": 28, "suggestions": "956"}, {"ruleId": "929", "severity": 1, "message": "955", "line": 127, "column": 6, "nodeType": "931", "endLine": 127, "endColumn": 28, "suggestions": "957"}, {"ruleId": "929", "severity": 1, "message": "958", "line": 227, "column": 6, "nodeType": "931", "endLine": 227, "endColumn": 77, "suggestions": "959"}, {"ruleId": "929", "severity": 1, "message": "960", "line": 337, "column": 6, "nodeType": "931", "endLine": 337, "endColumn": 132, "suggestions": "961"}, {"ruleId": "929", "severity": 1, "message": "962", "line": 382, "column": 6, "nodeType": "931", "endLine": 382, "endColumn": 45, "suggestions": "963"}, {"ruleId": "929", "severity": 1, "message": "962", "line": 391, "column": 6, "nodeType": "931", "endLine": 391, "endColumn": 45, "suggestions": "964"}, {"ruleId": "929", "severity": 1, "message": "965", "line": 523, "column": 5, "nodeType": "931", "endLine": 523, "endColumn": 112, "suggestions": "966"}, {"ruleId": "929", "severity": 1, "message": "967", "line": 222, "column": 8, "nodeType": "931", "endLine": 222, "endColumn": 82, "suggestions": "968"}, {"ruleId": "929", "severity": 1, "message": "955", "line": 106, "column": 6, "nodeType": "931", "endLine": 106, "endColumn": 17, "suggestions": "969"}, {"ruleId": "929", "severity": 1, "message": "962", "line": 299, "column": 6, "nodeType": "931", "endLine": 299, "endColumn": 66, "suggestions": "970"}, {"ruleId": "929", "severity": 1, "message": "971", "line": 412, "column": 6, "nodeType": "931", "endLine": 412, "endColumn": 34, "suggestions": "972"}, {"ruleId": "929", "severity": 1, "message": "962", "line": 420, "column": 6, "nodeType": "931", "endLine": 420, "endColumn": 34, "suggestions": "973"}, {"ruleId": "929", "severity": 1, "message": "974", "line": 554, "column": 5, "nodeType": "931", "endLine": 554, "endColumn": 124, "suggestions": "975"}, {"ruleId": "929", "severity": 1, "message": "976", "line": 34, "column": 6, "nodeType": "931", "endLine": 34, "endColumn": 20, "suggestions": "977"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchCategories'. Either include it or remove the dependency array.", "ArrayExpression", ["978"], "React Hook useEffect has a missing dependency: 'exam.duration'. Either include it or remove the dependency array.", ["979"], "React Hook useEffect has missing dependencies: 'fetchNearbyTutors' and 'fetchTutors'. Either include them or remove the dependency array.", ["980"], ["981"], "React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.", ["982"], ["983"], "React Hook useEffect has missing dependencies: 'enterFullscreen' and 'isFullscreen'. Either include them or remove the dependency array.", ["984"], ["985"], "prefer-const", "'minutes' is never reassigned. Use 'const' instead.", "Identifier", "useConst", ["986"], "React Hook useEffect has a missing dependency: 'fetchConstants'. Either include it or remove the dependency array.", ["987"], ["988"], "React Hook useEffect has a missing dependency: 'fetchQuestions'. Either include it or remove the dependency array.", ["989"], ["990"], "React Hook useEffect has a missing dependency: 'initializeViolationCounts'. Either include it or remove the dependency array.", ["991"], ["992"], "React Hook useCallback has missing dependencies: 'isCameraReady', 'isSubmitted', and 'router'. Either include them or remove the dependency array.", ["993"], "React Hook useEffect has a missing dependency: 'userAnswers'. Either include it or remove the dependency array.", ["994"], "React Hook useEffect has a missing dependency: 'exitFullScreen'. Either include it or remove the dependency array.", ["995"], ["996"], "React Hook useCallback has unnecessary dependencies: 'examIdStr' and 'studentId'. Either exclude them or remove the dependency array.", ["997"], "React Hook useEffect has missing dependencies: 'currentRoomId', 'offlineMessageUsers', and 'selectedUserId'. Either include them or remove the dependency array.", ["998"], ["999"], ["1000"], "React Hook useEffect has missing dependencies: 'exitFullScreen' and 'showTermination'. Either include them or remove the dependency array.", ["1001"], ["1002"], "React Hook useCallback has an unnecessary dependency: 'studentId'. Either exclude it or remove the dependency array.", ["1003"], "React Hook useEffect has a missing dependency: 'router'. Either include it or remove the dependency array.", ["1004"], {"desc": "1005", "fix": "1006"}, {"desc": "1007", "fix": "1008"}, {"desc": "1009", "fix": "1010"}, {"kind": "1011", "justification": "1012"}, {"desc": "1013", "fix": "1014"}, {"kind": "1011", "justification": "1012"}, {"desc": "1015", "fix": "1016"}, {"kind": "1011", "justification": "1012"}, {"kind": "1011", "justification": "1012"}, {"desc": "1017", "fix": "1018"}, {"kind": "1011", "justification": "1012"}, {"desc": "1019", "fix": "1020"}, {"kind": "1011", "justification": "1012"}, {"desc": "1021", "fix": "1022"}, {"desc": "1021", "fix": "1023"}, {"desc": "1024", "fix": "1025"}, {"desc": "1026", "fix": "1027"}, {"desc": "1028", "fix": "1029"}, {"desc": "1030", "fix": "1031"}, {"desc": "1032", "fix": "1033"}, {"desc": "1034", "fix": "1035"}, {"desc": "1036", "fix": "1037"}, {"desc": "1038", "fix": "1039"}, {"desc": "1040", "fix": "1041"}, {"desc": "1042", "fix": "1043"}, {"desc": "1044", "fix": "1045"}, {"desc": "1046", "fix": "1047"}, "Update the dependencies array to be: [fetchCategories]", {"range": "1048", "text": "1049"}, "Update the dependencies array to be: [exam.start_date, exam.start_registration_date, exam.id, exam.duration]", {"range": "1050", "text": "1051"}, "Update the dependencies array to be: [page, useNearby, userLocation, distance, fetchNearbyTutors, fetchTutors]", {"range": "1052", "text": "1053"}, "directive", "", "Update the dependencies array to be: [authError, dispatch]", {"range": "1054", "text": "1055"}, "Update the dependencies array to be: [escAttempts, showWarningDialog, showTerminationDialog, lastViolationTime, isExitingForSubmit, classId, examId, isMobile, isFullscreen, enterFullscreen]", {"range": "1056", "text": "1057"}, "Update the dependencies array to be: [fetchConstants]", {"range": "1058", "text": "1059"}, "Update the dependencies array to be: [currentPage, limit, filtersApplied, fetchQuestions]", {"range": "1060", "text": "1061"}, "Update the dependencies array to be: [studentId, examIdStr, initializeViolationCounts]", {"range": "1062", "text": "1063"}, {"range": "1064", "text": "1063"}, "Update the dependencies array to be: [isSubmitted, studentId, examIdStr, currentQuestionIndex, questions, isCameraReady, router, selectedAnswer]", {"range": "1065", "text": "1066"}, "Update the dependencies array to be: [currentQuestionIndex, questions, studentId, examIdStr, isDialogOpen, showTermination, isLoginDialogOpen, isProfileDialogOpen, userAnswers]", {"range": "1067", "text": "1068"}, "Update the dependencies array to be: [isQuizCompleted, studentId, examIdStr, exitFullScreen]", {"range": "1069", "text": "1070"}, "Update the dependencies array to be: [showTermination, studentId, examIdStr, exitFullScreen]", {"range": "1071", "text": "1072"}, "Update the dependencies array to be: [isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showWarning, isApiCallPending]", {"range": "1073", "text": "1074"}, "Update the dependencies array to be: [username, isUsernameSet, isAuthenticated, userType, userId, selectedUser, selectedUserId, offlineMessageUsers, currentRoomId]", {"range": "1075", "text": "1076"}, "Update the dependencies array to be: [initializeViolationCounts, studentId]", {"range": "1077", "text": "1078"}, "Update the dependencies array to be: [isQuizCompleted, studentId, calculateScore, calculateCoins, exitFullScreen]", {"range": "1079", "text": "1080"}, "Update the dependencies array to be: [exitFullScreen, isQuizCompleted, showTermination, studentId]", {"range": "1081", "text": "1082"}, "Update the dependencies array to be: [exitFullScreen, showTermination, studentId]", {"range": "1083", "text": "1084"}, "Update the dependencies array to be: [isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showWarning, isApiCallPending, isExamTakenDialogOpen]", {"range": "1085", "text": "1086"}, "Update the dependencies array to be: [router, searchParams]", {"range": "1087", "text": "1088"}, [7243, 7245], "[fetchCategories]", [3832, 3888], "[exam.start_date, exam.start_registration_date, exam.id, exam.duration]", [7250, 7291], "[page, useNearby, userLocation, distance, fetchNearbyTutors, fetchTutors]", [622, 633], "[auth<PERSON><PERSON><PERSON>, dispatch]", [6888, 7055], "[escAttempts, showWarningDialog, showTerminationDialog, lastViolationTime, isExitingForSubmit, classId, examId, isMobile, isFullscreen, enterFullscreen]", [7258, 7260], "[fetchConstants]", [7403, 7439], "[currentPage, limit, filtersApplied, fetchQuestions]", [4115, 4137], "[studentId, examIdStr, initializeViolationCounts]", [4569, 4591], [7671, 7742], "[isSubmitted, studentId, examIdStr, currentQuestionIndex, questions, isCameraReady, router, selectedAnswer]", [11908, 12034], "[currentQuestionIndex, questions, studentId, examIdStr, isDialogOpen, showTermination, isLoginDialogOpen, isProfileDialogOpen, userAnswers]", [13909, 13948], "[isQuizCompleted, studentId, examIdStr, exitFullScreen]", [14239, 14278], "[showTermination, studentId, examIdStr, exitFullScreen]", [19609, 19716], "[isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showWarning, isApiCallPending]", [9360, 9434], "[username, isUsernameSet, isAuthenticated, userType, userId, selectedUser, selectedUserId, offlineMessageUsers, currentRoomId]", [4060, 4071], "[initializeViolationCounts, studentId]", [10723, 10783], "[isQuizCompleted, studentId, calculateScore, calculateCoins, exitFullScreen]", [14905, 14933], "[exitFullScreen, isQuizCompleted, showTermination, studentId]", [15142, 15170], "[exitFullScreen, showTermination, studentId]", [20434, 20553], "[isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showWarning, isApiCallPending, isExamTakenDialogOpen]", [1026, 1040], "[router, searchParams]"]