<?php

namespace Admission\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class StudentParentsDetails extends Model
{
    use HasFactory, SoftDeletes;
    public $table = 'student_parents_details';

    protected $fillable = [
        'student_id',
        'fathers_name',
        'fathers_middle_name',
        'fathers_last_name',
        'fathers_aadhaar_no',
        'fathers_qualification',
        'fathers_occupation',
        'mothers_name',
        'mothers_middle_name',
        'mothers_last_name',
        'mothers_aadhaar_no',
        'mothers_qualification',
        'mothers_occupation',
        'contact_no_1',
        'contact_no_2',
        'family_income',
        'part_of_ngo',
    ];
}
