<?php

namespace Admission\Models;

use Classroom\Models\Classroom;
use Department\Models\Department;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Years\Models\Years;

class StudentAcademicInfo extends Model
{
    use HasFactory, SoftDeletes;
    public $table = 'student_academic_info';

    protected $fillable = [
        'student_id',
        'department',
        'classroom',
        'year',
    ];

    public function getDepartment()
    {
        return $this->belongsTo(Department::class, 'department', 'id')->select('name', 'id');
    }

    public function getClassroom()
    {
        return $this->belongsTo(Classroom::class, 'classroom', 'id')->select('class_name', 'id');
    }

    public function getYear()
    {
        return $this->belongsTo(Years::class, 'year', 'id');
    }
}
