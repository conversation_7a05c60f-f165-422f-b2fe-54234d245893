<?php

namespace Admission\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateStudentParentsDetailsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'fathers_name' => 'required|string|max:200',
            "fathers_middle_name"    => "required|string|max:200",
            "fathers_last_name"    => "required|string|max:200",
            'fathers_qualification' => 'nullable|string|max:200',
            'fathers_occupation' => 'nullable|string|max:200',
            'fathers_aadhaar_no' => 'nullable|numeric|digits_between:1,15',

            'mothers_name' => 'required|string|max:200',
            "mothers_middle_name"    => "required|string|max:200",
            "mothers_last_name"    => "required|string|max:200",
            'mothers_qualification' => 'nullable|string|max:200',
            'mothers_occupation' => 'nullable|string|max:200',
            'mothers_aadhaar_no' => 'nullable|numeric|digits_between:1,15',

            "contact_no_1"    => "required|numeric|digits_between:7,13",
            "contact_no_2"    => "nullable|numeric|digits_between:7,13",
            'email' => 'nullable|email|max:200',

            'family_income' => 'nullable|string|max:200',
            'part_of_ngo' => 'nullable|string|max:200',
        ];
    }
}
