import prisma from '@/config/prismaClient';
import { Status } from '@prisma/client';
import { getConstantsByCategory } from './constantService';


export const createStudentProfile = async (
  studentId: string,
  medium: string,
  classroom: string,
  birthday: Date,
  school: string,
  address: string,
  photo?: string,
  documentUrl?: string,
  status: Status = 'PENDING'
) => {
  return prisma.studentProfile.create({
    data: {
      studentId,
      medium,
      classroom,
      birthday,
      school,
      address,
      photo,
      documentUrl,
      status,
    },
  });
};


export const getStudentProfileByStudentId = async (studentId: string) => {
  return prisma.studentProfile.findUnique({
    where: {
      studentId,
    },
    include: {
      student: true,
    },
  });
};

export const getStudentWithProfileAndOptions = async (studentId: string) => {
    // Get student profile with student data
  const profile = await prisma.studentProfile.findUnique({
    where: {
      studentId,
    },
    include: {
      student: true,
    },
  });

  // Get student coins
  const coins = await prisma.uestCoins.findFirst({
    where: {
      modelType: 'STUDENT',
      modelId: studentId,
    },
  });

  const tuitionClassesCategory = await getConstantsByCategory('TuitionClasses');
  const educationDetail = tuitionClassesCategory?.details?.find(d => d.name === 'Education');
  const classroomSubDetail = educationDetail?.subDetails?.find(sd => sd.name === 'classroom');
  const classroomOptions = classroomSubDetail?.values?.map(val => ({ id: val.id, value: val.name })) || [];

    // Combine all data
  return {
    profile,
    coins: coins?.coins ?? 0,
    classroomOptions,
  };
};

// Get student data with profile for admin
export const getStudentWithProfileForAdmin = async (studentId: string) => {
  // Get student profile with student data
  const profile = await prisma.studentProfile.findUnique({
    where: {
      studentId,
    },
    include: {
      student: true,
    },
  });

  // Get student coins
  const coins = await prisma.uestCoins.findFirst({
    where: {
      modelType: 'STUDENT',
      modelId: studentId,
    },
  });

  const tuitionClassesCategory = await getConstantsByCategory('TuitionClasses');
  const educationDetail = tuitionClassesCategory?.details?.find(d => d.name === 'Education');
  const classroomSubDetail = educationDetail?.subDetails?.find(sd => sd.name === 'classroom');
  const classroomOptions = classroomSubDetail?.values?.map(val => ({ id: val.id, value: val.name })) || [];

  // If profile exists, add coins to student data
  if (profile && profile.student) {
    (profile.student as any).coins = coins?.coins ?? 0;
  }

  return {
    profile,
    classroomOptions,
  };
};


export const updateStudentProfile = async (
  studentId: string,
  data: {
    medium?: string;
    classroom?: string;
    birthday?: Date;
    school?: string;
    address?: string;
    photo?: string;
    documentUrl?: string;
    status?: Status;
  }
) => {
  return prisma.studentProfile.update({
    where: {
      studentId,
    },
    data,
  });
};


export const updateStudentProfileStatus = async (
  studentId: string,
  status: Status
) => {
  return prisma.studentProfile.update({
    where: {
      studentId,
    },
    data: {
      status,
    },
  });
};


export const deleteStudentProfile = async (studentId: string) => {
  return prisma.studentProfile.delete({
    where: {
      studentId,
    },
  });
};

// Update both student and profile data in one transaction
export const updateStudentAndProfile = async (
  studentId: string,
  studentData: {
    firstName?: string;
    lastName?: string;
    contact?: string;
  },
  profileData: {
    medium?: string;
    classroom?: string;
    birthday?: Date;
    school?: string;
    address?: string;
    photo?: string;
    documentUrl?: string;
  }
) => {
  return prisma.$transaction(async (tx) => {
    // Update student data if provided
    if (Object.keys(studentData).length > 0) {
      await tx.student.update({
        where: { id: studentId },
        data: studentData,
      });
    }

    // Check if profile exists
    const existingProfile = await tx.studentProfile.findUnique({
      where: { studentId },
    });

    if (Object.keys(profileData).length > 0) {
      if (existingProfile) {
        await tx.studentProfile.update({
          where: { studentId },
          data: profileData,
        });
      } else {
        if (!profileData.medium || !profileData.classroom || !profileData.birthday || !profileData.school || !profileData.address) {
          throw new Error('Missing required profile fields for new profile creation');
        }

        await tx.studentProfile.create({
          data: {
            studentId,
            medium: profileData.medium,
            classroom: profileData.classroom,
            birthday: profileData.birthday,
            school: profileData.school,
            address: profileData.address,
            photo: profileData.photo,
            documentUrl: profileData.documentUrl,
            status: 'PENDING',
          },
        });
      }
    }

    // Return the updated data
    const updatedProfile = await tx.studentProfile.findUnique({
      where: { studentId },
      include: { student: true },
    });

    return updatedProfile;
  });
};

export const deleteStudentService = async (studentId: string) => {
  try {
    await prisma.$transaction(async (tx) => {
      await Promise.all([
        tx.studentProfile.deleteMany({ where: { studentId } }), 
        tx.studentWishlist.deleteMany({ where: { studentId } }),
        tx.classesReviews.deleteMany({ where: { studentId } }),
        tx.uestCoins.deleteMany({ where: { modelId: studentId, modelType: 'STUDENT' } }),
        tx.uestCoinTransaction.deleteMany({ where: { modelId: studentId, modelType: 'STUDENT' } }),
        tx.referralEarning.deleteMany({ where: { studentId } }),
      ]);

      await tx.student.delete({ where: { id: studentId } });
    });

  } catch (error: any) {
    console.error(`Error in deleteStudentService for studentId ${studentId}:`, {
      message: error.message,
      code: error.code,
      stack: error.stack,
    });
    throw error;
  }
};
