import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export interface PaginationParams {
    page: number;
    limit: number;
    search?: string;
}

export interface PaginatedResult<T> {
    data: T[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };
}

export const getAllClasses = async (params?: PaginationParams): Promise<PaginatedResult<any> | any[]> => {
    if (!params) {
        const classes = await prisma.classes.findMany({
            select: {
                id: true,
                firstName: true,
                lastName: true,
                className: true,
                email: true,
                username: true,
                createdAt: true
            }
        });

        const classesWithLatestMessage = await Promise.all(
            classes.map(async (classItem) => {
                const latestMessage = await prisma.chatMessage.findFirst({
                    where: {
                        OR: [
                            { senderId: classItem.id, senderType: 'class' },
                            { recipientId: classItem.id, recipientType: 'class' }
                        ]
                    },
                    orderBy: {
                        timestamp: 'desc'
                    },
                    select: {
                        timestamp: true
                    }
                });

                return {
                    ...classItem,
                    latestMessageTime: latestMessage?.timestamp || classItem.createdAt
                };
            })
        );

        return classesWithLatestMessage.sort((a, b) =>
            new Date(b.latestMessageTime).getTime() - new Date(a.latestMessageTime).getTime()
        );
    }

    const { page, limit, search } = params;
    const skip = (page - 1) * limit;

    const whereClause = search ? {
        OR: [
            { firstName: { contains: search, mode: 'insensitive' as const } },
            { lastName: { contains: search, mode: 'insensitive' as const } },
            { email: { contains: search, mode: 'insensitive' as const } },
            { className: { contains: search, mode: 'insensitive' as const } }
        ]
    } : {};

    const total = await prisma.classes.count({ where: whereClause });

    const classes = await prisma.classes.findMany({
        where: whereClause,
        select: {
            id: true,
            firstName: true,
            lastName: true,
            className: true,
            email: true,
            username: true,
            createdAt: true
        }
    });

    const classesWithLatestMessage = await Promise.all(
        classes.map(async (classItem) => {
            const latestMessage = await prisma.chatMessage.findFirst({
                where: {
                    OR: [
                        { senderId: classItem.id, senderType: 'class' },
                        { recipientId: classItem.id, recipientType: 'class' }
                    ]
                },
                orderBy: {
                    timestamp: 'desc'
                },
                select: {
                    timestamp: true
                }
            });

            return {
                ...classItem,
                latestMessageTime: latestMessage?.timestamp || classItem.createdAt
            };
        })
    );

    const sortedClasses = classesWithLatestMessage.sort((a, b) =>
        new Date(b.latestMessageTime).getTime() - new Date(a.latestMessageTime).getTime()
    );

    const paginatedData = sortedClasses.slice(skip, skip + limit);
    const totalPages = Math.ceil(total / limit);

    return {
        data: paginatedData,
        pagination: {
            page,
            limit,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1
        }
    };
};

export const getAllStudents = async (params?: PaginationParams): Promise<PaginatedResult<any> | any[]> => {
    if (!params) {
        return await prisma.student.findMany({
            select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                createdAt: true
            },
            orderBy: {
                firstName: 'asc'
            }
        });
    }

    const { page, limit, search } = params;
    const skip = (page - 1) * limit;

    const whereClause = search ? {
        OR: [
            { firstName: { contains: search, mode: 'insensitive' as const } },
            { lastName: { contains: search, mode: 'insensitive' as const } },
            { email: { contains: search, mode: 'insensitive' as const } }
        ]
    } : {};

    const total = await prisma.student.count({ where: whereClause });

    const data = await prisma.student.findMany({
        where: whereClause,
        select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            createdAt: true
        },
        orderBy: {
            firstName: 'asc'
        },
        skip,
        take: limit
    });

    const totalPages = Math.ceil(total / limit);

    return {
        data,
        pagination: {
            page,
            limit,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1
        }
    };
};

export const getConversationBetweenUsers = async (classId: string, studentId: string) => {
    const classDetails = await prisma.classes.findUnique({
        where: { id: classId },
        select: { firstName: true, lastName: true, username: true }
    });

    const studentDetails = await prisma.student.findUnique({
        where: { id: studentId },
        select: { firstName: true, lastName: true }
    });

    if (!classDetails || !studentDetails) {
        throw new Error("Class or Student not found");
    }

    const className = `${classDetails.firstName} ${classDetails.lastName}`;
    const studentName = `${studentDetails.firstName} ${studentDetails.lastName}`;

    const messages = await prisma.chatMessage.findMany({
        where: {
            OR: [
                { senderId: classId, recipientId: studentId },
                { senderId: studentId, recipientId: classId }
            ]
        },
        orderBy: {
            timestamp: 'asc'
        }
    });

    return {
        classDetails: {
            id: classId,
            name: className,
            username: classDetails.username
        },
        studentDetails: {
            id: studentId,
            name: studentName
        },
        messages
    };
};

export const getStudentsChattedWithClass = async (classId: string) => {
    const messages = await prisma.chatMessage.findMany({
        where: {
            OR: [
                { senderId: classId, senderType: 'class' },
                { recipientId: classId, recipientType: 'class' }
            ]
        },
        select: {
            senderId: true,
            senderType: true,
            recipientId: true,
            recipientType: true
        }
    });

    const studentIds = new Set<string>();
    messages.forEach(message => {
        if (message.senderId !== classId && message.senderType === 'student') {
            studentIds.add(message.senderId);
        }
        if (message.recipientId !== classId && message.recipientType === 'student' && message.recipientId) {
            studentIds.add(message.recipientId);
        }
    });

    const students = await prisma.student.findMany({
        where: {
            id: {
                in: Array.from(studentIds)
            }
        },
        select: {
            id: true,
            firstName: true,
            lastName: true
        }
    });

    return students.map(student => `${student.firstName} ${student.lastName}`);
};

export const getStudentsChattedWithClassPaginated = async (classId: string, params: PaginationParams): Promise<PaginatedResult<any>> => {
    const { page, limit, search } = params;
    const skip = (page - 1) * limit;

    const messages = await prisma.chatMessage.findMany({
        where: {
            OR: [
                { senderId: classId, senderType: 'class' },
                { recipientId: classId, recipientType: 'class' }
            ]
        },
        select: {
            senderId: true,
            senderType: true,
            recipientId: true,
            recipientType: true
        }
    });

    const studentIds = new Set<string>();
    messages.forEach(message => {
        if (message.senderId !== classId && message.senderType === 'student') {
            studentIds.add(message.senderId);
        }
        if (message.recipientId !== classId && message.recipientType === 'student' && message.recipientId) {
            studentIds.add(message.recipientId);
        }
    });

    const whereClause: any = {
        id: {
            in: Array.from(studentIds)
        }
    };

    if (search) {
        whereClause.OR = [
            { firstName: { contains: search, mode: 'insensitive' as const } },
            { lastName: { contains: search, mode: 'insensitive' as const } },
            { email: { contains: search, mode: 'insensitive' as const } }
        ];
    }

    const total = await prisma.student.count({ where: whereClause });

    const students = await prisma.student.findMany({
        where: whereClause,
        select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            createdAt: true
        }
    });

    const studentsWithLatestMessage = await Promise.all(
        students.map(async (student) => {
            const latestMessage = await prisma.chatMessage.findFirst({
                where: {
                    OR: [
                        { senderId: student.id, recipientId: classId },
                        { senderId: classId, recipientId: student.id }
                    ]
                },
                orderBy: {
                    timestamp: 'desc'
                },
                select: {
                    timestamp: true
                }
            });

            return {
                ...student,
                latestMessageTime: latestMessage?.timestamp || student.createdAt
            };
        })
    );

    const sortedStudents = studentsWithLatestMessage.sort((a, b) =>
        new Date(b.latestMessageTime).getTime() - new Date(a.latestMessageTime).getTime()
    );

    const paginatedData = sortedStudents.slice(skip, skip + limit);
    const totalPages = Math.ceil(total / limit);

    return {
        data: paginatedData,
        pagination: {
            page,
            limit,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1
        }
    };
};