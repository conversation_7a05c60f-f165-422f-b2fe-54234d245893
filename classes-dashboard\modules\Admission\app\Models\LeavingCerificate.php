<?php

namespace Admission\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class LeavingCerificate extends Model
{
    use HasFactory, SoftDeletes;
    public $table = 'student_leaving_certificate';

    protected $fillable = [
        'student_id',
        'student_name',
        'mothers_name',
        'place_of_birth',
        'nationality',
        'category',
        'date_of_birth',
        'last_school_attained',
        'date_of_admission',
        'admission_standard',
        'date_of_leaving_school',
        'standard',
        'reason_for_leaving',
        'attendance',
        'progress',
        'conduct',
        'remarks',
    ];
}
