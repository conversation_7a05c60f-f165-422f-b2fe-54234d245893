let columns = [
    {
        data: 'action',
        name: 'action',
        orderable: false
    },
    {
        data: 'photo',
        name: 'photo',
        orderable: false
    },
    {
        data: 'created_at',
        name: 'created_at',
    },
    {
        data: 'student_full_name',
        name: 'student_full_name',
    },
    {
        data: 'contact_no',
        name: 'contact_no',
    },
    {
        data: 'year_name',
        name: 'year_name',
    },
    {
        data: 'class_info',
        name: 'class_info',
    },
    {
        data: 'gr_no',
        name: 'gr_no',
    },
];

let data = function(d) {

    const params = new URLSearchParams(window.location.search);
    const vehicleId = params.get('vehicle_id');
    const waypointid = params.get('waypoint_id');
    const routeid = params.get('route_id');

    d.year = $('#year_filter').val()
        d.department = $('#department').val()
        d.classroom = $('#classroom-filter').val()
        d.status = $('#status-filter').val()
        d.vehicle_id = vehicleId
        d.waypoint_id = waypointid
        d.route_id = routeid
}

let table = commonDatatable('#admissions_table', admissionRoute.index, columns, data);

function tablescroll() {
    $('html, body').animate({
        scrollTop: $("#admissions_table").offset().top
    }, 1000, );
}

$("#filter").on('click', function(event) {
    event.preventDefault();
    tablescroll();
    table.draw();
});

$('#filterreset').click(function() {
    event.preventDefault();
    $('#year_filter').val("").trigger('change');
    $('#department').val("").trigger('change');
    $('#classroom-filter').val("").trigger('change');
    $('#status-filter').val("").trigger('change');
    tablescroll();
    table.draw();
});

$(document).on("click", ".exportData", function () {
    var url = admissionRoute.export;
    var data = {
        year : $('#year_filter').val(),
        department : $('#department').val(),
        classroom : $('#classroom-filter').val(),
        status : $('#status-filter').val(),
    };

    exportData(url, data);
});

$(document).on("click", ".addRoutes", function () {
    let params = $.extend({}, doAjax_params_default);
    let id = $(this).attr("data-studentid");
    let url = admissionRoute.showRoutes;
    url = url.replace(":id", id);

    params["url"] = url;
    params["requestType"] = `GET`;
    params["successCallbackFunction"] = function successCallbackFunction(
        result
    ) {
        $("#modeltitle").html("Add New Waypoints to Student");
        $("#createContent").html(result);
    };
    commonAjax(params);
});