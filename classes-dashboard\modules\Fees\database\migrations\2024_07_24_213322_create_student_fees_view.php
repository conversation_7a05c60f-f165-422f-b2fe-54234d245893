<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class CreateStudentFeesView extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement("DROP VIEW IF EXISTS student_fees_view");
        DB::statement("
            CREATE VIEW student_fees_view AS
            SELECT 
                student_details.id,
                student_details.first_name,
                student_details.middle_name,
                student_details.last_name,
                student_details.gr_no,
                classrooms.id AS class_id,
                classrooms.class_name,
                years.year_name AS year_name,
                years.id AS year_id,
                classroom_category_fees_details.id AS fee_detail_id,
                classroom_category_fees_details.amount,
                fees_category.category_name AS fees_category,
                fees_category.id AS fees_category_id,
                classroom_fees_details.lock AS fee_locked
            FROM 
                student_details
                LEFT JOIN student_academic_info ON student_details.id = student_academic_info.student_id
                LEFT JOIN years ON student_academic_info.year = years.id
                LEFT JOIN department ON student_academic_info.department = department.id
                LEFT JOIN classrooms ON student_academic_info.classroom = classrooms.id
                LEFT JOIN classroom_fees_details ON classrooms.id = classroom_fees_details.classroom_id 
                    AND classroom_fees_details.year_id = student_academic_info.year 
                LEFT JOIN classroom_category_fees_details ON classroom_fees_details.id = classroom_category_fees_details.classroom_fees_details_id
                LEFT JOIN fees_category ON fees_category.id = classroom_category_fees_details.category_id 
                    AND fees_category.deleted_at IS NULL
            WHERE 
                student_details.deleted_at IS NULL
        ");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement("DROP VIEW IF EXISTS student_fees_view");
    }
}