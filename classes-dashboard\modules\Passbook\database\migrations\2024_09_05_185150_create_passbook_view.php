<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class CreatePassbookView extends Migration
{
    public function up()
    {
        DB::statement('DROP VIEW IF EXISTS passbook');

        DB::statement(<<<SQL
        CREATE VIEW passbook AS
        SELECT
            ('FEES_' || spd.id)::text as pid,
            'Fees'::text as source,
            spd.paid_amount::numeric as credit,
            0::numeric as debit,
            spd.payment_date::date as date,
            spd.payment_mode::text,
            s.id::integer as user_id,
            (s.first_name || ' ' || s.last_name)::text as name,
            s.class_uuid as class_uuid
        FROM student_payment_details spd
        LEFT JOIN student_details s ON spd.student_id = s.id
        WHERE spd.deleted_at IS NULL

        UNION ALL

        SELECT
            ('ENQ_' || ef.id)::text as pid,
            (CASE 
                WHEN ef.enquiry_status = 'SHORTLISTED' THEN 'Form Fee'
                ELSE 'Admission Fee'
            END)::text AS source,
            ef.paid_amount::numeric as credit,
            0::numeric as debit,
            ef.payment_date::date as date,
            ef.payment_mode::text,
            e.id::integer as user_id,
            (e.student_first_name || ' ' || e.student_last_name)::text as name,
            e.class_uuid as class_uuid
        FROM enquiry_fees ef
        LEFT JOIN enquiry e ON e.id = ef.enquiry_id
        WHERE ef.deleted_at IS NULL
        SQL);
    }

    public function down()
    {
        DB::statement('DROP VIEW IF EXISTS passbook');
    }
}