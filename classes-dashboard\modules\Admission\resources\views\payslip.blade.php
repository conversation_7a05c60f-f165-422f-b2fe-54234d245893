<html>
<head>
    <meta charset="utf-8">
    <title>Fee Receipt</title>
    <link href="{{ $tenantLogo }}" rel="icon">
    <link rel="stylesheet" href="{{asset('assets/css/slips.css')}}">
    <style>
        table.meta, table.balance {
            width: 50% !important;
        }
    </style>
</head>

<body id="txtContent">
    <header>
        <span><img alt="" class="logo" src="{{ $tenantLogo }}"></span>
        <address>
            <p>{{ $tenantAddress }}</p>
        </address>
    </header>
    <article>
        <h1><b>Fee Receipt</b></h1>
        <table class="metae">
            <tr>
                <th>Student Name</th>
                <td>{{ $studentdata->first_name }} {{ $studentdata->middle_name }} {{ $studentdata->last_name }}</td>
            </tr>
            <tr>
                <th>Student Id</th>
                <td> {{ $studentdata->gr_no }}</td>
            </tr>
            <tr>
                <th>Standard</th>
                <td>{{ $studentdata->getAcademicInfo->getClassroom->class_name }}</td>
            </tr>
            <tr>
                <th>Medium</th>
                <td>{{ $studentdata->getAcademicInfo->getDepartment->name }}</td>
            </tr>
        </table>
        <table class="meta">
        <tr>
                <th>Academic Year</th>
                <td>{{ $studentdata->getAcademicInfo->getYear->year_name }}</td>
            </tr>
            <tr>
                <th>Receipt No.</th>
                <td>{{$paymentdetails->id}}</td>
            </tr>
            <tr>
                <th>Payment Date</th>
                <td>{{date_formatter($paymentdetails->created_at)}}</td>
            </tr>
            <tr>
                <th>Payment Mode</th>
                <td>{{$paymentdetails->payment_mode}}</td>
            </tr>
        </table>
        <table class="inventory" cellspacing="0" cellpadding="0">
            <thead>
                <tr>
                    <th><span>Fee Name</span></th>
                    <th><span>Fee Amount (Rs.)</span></th>
                </tr>
            </thead>
            <tbody>
            @if(isset($paymentdetails->payment_category))
            @foreach(json_decode($paymentdetails->payment_category) as $category => $amount)
                <tr>
                    <td><span>{{ $category}}</span></td>
                    <td><span>{{ number_format($amount, 2, '.', '') }}</span></td>
                </tr>
            @endforeach
            @endif
            </tbody>
        </table>
        <table cellspacing="0" cellpadding="0">
            <tr>
                <th><span>Net Total</span></th>
                <td><span>{{ $paymentdetails->paid_amount }}</span></td>
            </tr>
            <tr>
                <th><span>Net Total in Words</span></th>
                <td><span>@php $digit = new NumberFormatter("en", NumberFormatter::SPELLOUT);@endphp 
                {{$digit->format($paymentdetails->paid_amount); }}</span></td>
            </tr>
        </table>
    </article>
</body>
</html>