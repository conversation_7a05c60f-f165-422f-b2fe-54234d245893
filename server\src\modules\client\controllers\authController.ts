import { Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import axios from 'axios';
import prisma from '@/config/prismaClient';
import {
  createUser,
  findUserByEmail,
  findUserByContactNo,
  createOtp,
  verifyOtp,
  findUserByContactNoForLogin,
  updateUserContact,
} from '@/modules/client/services/authService';
import { sendError, sendSuccess } from '@/utils/response';
import { transporter } from '@/utils/email';
import { createAdminNotificationTemplate, createVerificationEmailTemplate } from '@/utils/emailTemplates';
import { getReferralLinkByCode, createReferral } from '../services/referralService';
import { logLogin, logLogout, logRegistration } from '@/utils/activityLogger';
import { UserType } from '@prisma/client';

const JWT_SECRET = process.env.JWT_SECRET || 'secret123';
const COOKIE_NAME = 'client_jwt';

export const continueWithEmail = async (req: Request, res: Response): Promise<any> => {
  try {
    const { email } = req.body;
    const user = await findUserByEmail(email);
    if (!user) {
      return sendError(res, 'Email not registered. Please sign up.', 404);
    }

    const isOldUser = !user.contactNo;
    
    if (!isOldUser) {
      // If contactNo exists (isOldUser: false), send OTP directly
      const contactNo = user.contactNo!;
      const otpRecord = await createOtp(contactNo);

      const response = await axios.get(
        `${process.env.SHREE_TRIPADA_API_URL}?auth_key=${process.env.SHREE_TRIPADA_AUTH_KEY}&mobiles=91${contactNo}&templateid=${process.env.SHREE_TRIPADA_TEMPLATE_ID}&message=Hi ${user.firstName}, Please use the code ${otpRecord.otp} to log in to Uest EdTech. For your security, please do not share this code with anyone. Thank you!&sender=${process.env.SHREE_TRIPADA_SENDER}&route=4`
      );

      if (response.status !== 200 || response.data.status !== 'SMS Submitted Successfully') {
        return sendError(res, 'Failed to send OTP', 500);
      }

      return sendSuccess(res, {
        isOldUser: false,
        firstName: user.firstName,
        lastName: user.lastName,
        contactNo: user.contactNo,
        otpSent: true,
      }, 'OTP sent successfully');
    }

    // If no contactNo (isOldUser: true), ask for contact number
    return sendSuccess(res, {
      isOldUser: true,
      firstName: user.firstName,
      lastName: user.lastName,
      contactNo: null,
    }, 'Email check successful');
  } catch (error: any) {
    return sendError(res, `Internal server error: ${error.message}`, 500);
  }
};

export const registerUser = async (req: Request, res: Response): Promise<any> => {
  try {
    const { firstName, lastName, contactNo, referralCode, email } = req.body;

    const studentExists = await prisma.student.findFirst({
      where: { contact: contactNo },
    });
    if (studentExists) {
      return sendError(res, 'This mobile number is already registered as a student. Please use a different number.', 400);
    }

    const existingUser = await findUserByContactNo(contactNo);
    if (existingUser) {
      return sendError(res, 'Mobile number already registered. Please login.', 400);
    }

    const otpRecord = await createOtp(contactNo);

    const response = await axios.get(
      `${process.env.SHREE_TRIPADA_API_URL}?auth_key=${process.env.SHREE_TRIPADA_AUTH_KEY}&mobiles=91${contactNo}&templateid=${process.env.SHREE_TRIPADA_TEMPLATE_ID}&message=Hi ${firstName}, Please use the code ${otpRecord.otp} to log in to Uest EdTech. For your security, please do not share this code with anyone. Thank you!&sender=${process.env.SHREE_TRIPADA_SENDER}&route=4`
    );

    if (response.status !== 200 || response.data.status !== 'SMS Submitted Successfully') {
      console.log(`Shree Tripada API failed: ${JSON.stringify(response.data)}`);
      return sendError(res, 'Failed to send OTP', 500);
    }

    return sendSuccess(res, { firstName, lastName, referralCode, email }, 'OTP sent successfully');
  } catch (error: any) {
    console.error('Register error:', error);
    return sendError(res, `Internal server error: ${error.message}`, 500);
  }
};

export const loginUser = async (req: Request, res: Response): Promise<any> => {
  try {
    const { contactNo, email } = req.body;

    const studentExists = await prisma.student.findFirst({
      where: { contact: contactNo },
    });
    if (studentExists) {
      return sendError(res, 'This mobile number is already registered as a student. Please use a different number.', 400);
    }

    let firstName = 'User';
    let lastName = '';

    if (email) {
      const user = await findUserByEmail(email);
      if (!user) {
        return sendError(res, 'Email not registered. Please sign up.', 404);
      }
      firstName = user.firstName;
      lastName = user.lastName;
    } else {
      const user = await findUserByContactNoForLogin(contactNo);
      if (!user) {
        return sendError(res, 'Mobile number not registered. Please sign up.', 404);
      }
      if (!user.isVerified) {
        return sendError(res, 'Please verify your account', 403);
      }
      firstName = user.firstName;
      lastName = user.lastName;
    }

    const otpRecord = await createOtp(contactNo);

    const response = await axios.get(
      `${process.env.SHREE_TRIPADA_API_URL}?auth_key=${process.env.SHREE_TRIPADA_AUTH_KEY}&mobiles=91${contactNo}&templateid=${process.env.SHREE_TRIPADA_TEMPLATE_ID}&message=Hi ${firstName}, Please use the code ${otpRecord.otp} to log in to Uest EdTech. For your security, please do not share this code with anyone. Thank you!&sender=${process.env.SHREE_TRIPADA_SENDER}&route=4`
    );

    if (response.status !== 200 || response.data.status !== 'SMS Submitted Successfully') {
      return sendError(res, 'Failed to send OTP', 500);
    }

    return sendSuccess(res, {
      contactNo,
      firstName,
      lastName,
      email,
    }, 'OTP sent successfully');
  } catch (error: any) {
    console.error('Login error:', error);
    return sendError(res, `Internal server error: ${error.message}`, 500);
  }
};

export const verifyOtpController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { contactNo, otp, email, firstName, lastName, referralCode } = req.body;

    const otpRecord = await verifyOtp(contactNo, otp);
    if (!otpRecord) {
      return sendError(res, 'Invalid or expired OTP', 400);
    }

    let user = await findUserByContactNo(contactNo);

    if (user) {
      const token = jwt.sign(
        { id: user.id, contactNo: user.contactNo, isVerified: user.isVerified },
        JWT_SECRET,
        { expiresIn: '7d' }
      );

      res.cookie(COOKIE_NAME, token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000,
      });

      // Log login activity
      await logLogin(user.id, UserType.CLASS);

      return sendSuccess(res, {
        userId: user.id,
        contactNo: user.contactNo,
        firstName: user.firstName,
        lastName: user.lastName,
        token,
      }, 'Login successful');
    }

    if (email) {
      user = await findUserByEmail(email);
      if (!user) {
        return sendError(res, 'Email not registered', 404);
      }
      user = await updateUserContact(email, contactNo);
    } else {
      if (!firstName || !lastName) {
        return sendError(res, 'First name and last name required for registration', 400);
      }

      user = await createUser(firstName, lastName, contactNo);

      if (referralCode) {
        try {
          const referralLink = await getReferralLinkByCode(referralCode);
          if (referralLink && referralLink.isActive) {
            await createReferral(
              referralLink.id,
              user.id,
              'CLASS',
              `${firstName} ${lastName}`,
              ''
            );
          }
        } catch (error) {
          console.log('Referral tracking error:', error);
        }
      }

      const adminNotificationHtml = createAdminNotificationTemplate({
        firstName,
        lastName,
        email: email || '',
        contact: contactNo,
      });

      const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';

      await transporter.sendMail({
        from: process.env.EMAIL_USER,
        to: adminEmail,
        subject: 'New Class Registration',
        html: adminNotificationHtml,
      });
    }

    const token = jwt.sign(
      { id: user.id, contactNo: user.contactNo, isVerified: user.isVerified },
      JWT_SECRET,
      { expiresIn: '7d' }
    );

    res.cookie(COOKIE_NAME, token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60 * 1000,
    });

    if (email) {
      await logLogin(user.id, UserType.CLASS);
    } else {
      await logRegistration(user.id, UserType.CLASS);
    }

    return sendSuccess(res, {
      userId: user.id,
      contactNo: user.contactNo,
      firstName: user.firstName,
      lastName: user.lastName,
      token,
    }, email ? 'Contact number updated and login successful' : 'Registration successful');
  } catch (error: any) {
    console.error('Verify OTP error:', error);
    return sendError(res, `Internal server error: ${error.message}`, 500);
  }
};

export const resendOtp = async (req: Request, res: Response): Promise<any> => {
  try {
    const { contactNo, firstName } = req.body;

    const otpRecord = await createOtp(contactNo);

    const response = await axios.get(
      `${process.env.SHREE_TRIPADA_API_URL}?auth_key=${process.env.SHREE_TRIPADA_AUTH_KEY}&mobiles=91${contactNo}&templateid=${process.env.SHREE_TRIPADA_TEMPLATE_ID}&message=Hi ${firstName || 'User'}, Please use the code ${otpRecord.otp} to log in to Uest EdTech. For your security, please do not share this code with anyone. Thank you!&sender=${process.env.SHREE_TRIPADA_SENDER}&route=4`
    );

    if (response.status !== 200 || response.data.status !== 'SMS Submitted Successfully') {
      await prisma.otpMessage.deleteMany({ where: { contactNo } });
      return sendError(res, 'Failed to send OTP', 500);
    }

    return sendSuccess(res, null, 'New OTP sent');
  } catch (error: any) {
    if (error.message === 'Too many OTP requests, try again later') {
      return sendError(res, error.message, 429);
    }
    return sendError(res, `Internal server error: ${error.message}`, 500);
  }
};

export const logout = async (req: Request, res: Response): Promise<any> => {
  try {
    const token = req.cookies[COOKIE_NAME];
    if (token) {
      try {
        const decoded = jwt.verify(token, JWT_SECRET) as { id: string };
        await logLogout(decoded.id, UserType.CLASS);
      } catch (error) {
        console.log('Failed to decode token for logout logging:', error);
      }
    }

    res.clearCookie(COOKIE_NAME, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
    });

    return sendSuccess(res, null, 'Logged out successfully');
  } catch (error) {
    console.error('Logout error:', error);
    return sendSuccess(res, null, 'Logged out successfully');
  }
};

export const verifyEmail = async (req: Request, res: Response): Promise<any> => {
  try {
    const { token } = req.query;
    const decode = jwt.verify(token as string, JWT_SECRET) as any;

    const classes = await prisma.classes.findUnique({
      where: { id: decode.id as string },
    });

    if (!classes) {
      return res.status(400).json({ message: "Invalid or expired token" });
    }

    if (classes.isVerified) {
      return res.json({ message: "Email already verified" });
    }

    const classToken = jwt.sign({ id: classes.id, email: classes.email }, JWT_SECRET, {
      expiresIn: "1d",
    });

    await prisma.classes.update({
      where: { id: classes.id },
      data: { isVerified: true },
    });

    res.cookie(COOKIE_NAME, classToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 24 * 60 * 60 * 1000,
    });

    res.json({ message: "Email verified successfully!", token: classToken });
  } catch {
    res.status(400).json({ message: "Invalid or expired token" });
  }
};

export const resendVerificationEmail = async (req: Request, res: Response): Promise<any> => {
  const { email } = req.body;

  const user = await findUserByEmail(email);
  if (!user) return sendError(res, "User not found", 404);
  if (user.isVerified) return sendError(res, "Email already verified", 400);

  const token = jwt.sign({ id: user.id, email: (user as any).email }, JWT_SECRET, {
    expiresIn: "1d",
  });

  const verificationURL = `${process.env.FRONTEND_URL}/verify-email?token=${token}&email=${email}`;

  const emailHtml = createVerificationEmailTemplate(verificationURL, true);

  await transporter.sendMail({
    from: process.env.EMAIL_USER,
    to: email,
    subject: "Email Verification : Uest.in",
    html: emailHtml,
  });

  res.json({ message: "Verification email sent" });
};

export const generateJWT = async (
  req: Request,
  res: Response
): Promise<any> => {
  const { contact } = req.body;

  const user = await findUserByContactNo(contact);
  if (!user) return sendError(res, "User not found", 404);

  const token = jwt.sign(
    { id: user.id, contactNo: user.contactNo, isVerified: user.isVerified },
    JWT_SECRET,
    {
      expiresIn: "1d",
    }
  );

  return sendSuccess(res, { user, token }, "Login successful");
};