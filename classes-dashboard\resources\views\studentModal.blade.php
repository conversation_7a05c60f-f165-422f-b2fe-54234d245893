@can('read student')
    <div class="modal" id="studentGlobalModal" role="dialog" aria-labelledby="roleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg salary-o-popup" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modeltitle" class="box-title popup-title m-0">Students Data</h3>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="card">
                        <div class="card-body">
                            <input name="department_global_search" type="hidden" id="department_global_search" />
                            <input name="classroom_global_search" type="hidden" id="classroom_global_search" />
                            <table id="studentSearchTable" class="table display  table-striped  table-borderless dt-responsive">
                                <thead>
                                    <tr>
                                        <th>Action</th>
                                        <th>Name</th>
                                        <th>Gr No</th>
                                        <th>Contact No</th>
                                        <th>Classroom</th>
                                        <th>Department</th>
                                    </tr>
                                </thead>
                                <tfoot>
                                    <tr class="search-row">
                                        <th>Action</th>
                                        <th>Name</th>
                                        <th>Gr No</th>
                                        <th>Contact No</th>
                                        <th>Classroom</th>
                                        <th>Department</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>

                    </div>
                </div>

            </div>
        </div>
    </div>
@endcan